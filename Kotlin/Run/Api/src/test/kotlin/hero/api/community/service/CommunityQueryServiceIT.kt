package hero.api.community.service

import hero.api.community.service.CommunitiesSortingFields.CREATED_AT
import hero.api.community.service.CommunitiesSortingFields.THREADS_COUNT
import hero.baseutils.minus
import hero.core.data.PageRequest
import hero.core.data.Sort
import hero.exceptions.http.NotFoundException
import hero.model.CommunityMemberStatus
import hero.test.IntegrationTest
import org.assertj.core.api.Assertions.assertThat
import org.assertj.core.api.Assertions.assertThatExceptionOfType
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.Test
import java.time.Instant
import java.util.UUID
import kotlin.time.Duration.Companion.seconds

class CommunityQueryServiceIT : IntegrationTest() {
    @Nested
    inner class GetCommunity {
        @Test
        fun `should get community by id`() {
            val underTest = prepareService()

            val owner = testHelper.createUser("owner-id")
            val community = testHelper.createCommunity(
                ownerId = "owner-id",
                name = "Test Community",
                description = "Test Description",
                slug = "test-community",
            )

            val result = underTest.execute(GetCommunity(community.id, null, null))

            with(result.community) {
                assertThat(this).isEqualTo(community)
                assertThat(id).isEqualTo(community.id)
                assertThat(name).isEqualTo("Test Community")
                assertThat(description).isEqualTo("Test Description")
                assertThat(slug).isEqualTo("test-community")
                assertThat(ownerId).isEqualTo("owner-id")
            }

            assertThat(result.owner).isEqualTo(owner)
            assertThat(result.isMember).isFalse()
        }

        @Test
        fun `should get community by slug`() {
            val underTest = prepareService()

            val owner = testHelper.createUser("owner-id")
            val community = testHelper.createCommunity(
                ownerId = "owner-id",
                name = "Test Community",
                description = "Test Description",
                slug = "test-community-slug",
            )

            val result = underTest.execute(GetCommunity(null, "test-community-slug", null))

            with(result.community) {
                assertThat(this).isEqualTo(community)
                assertThat(name).isEqualTo("Test Community")
                assertThat(description).isEqualTo("Test Description")
                assertThat(slug).isEqualTo("test-community-slug")
                assertThat(ownerId).isEqualTo("owner-id")
            }
            assertThat(result.owner).isEqualTo(owner)
        }

        @Test
        fun `owner is always part of community`() {
            val underTest = prepareService()

            val owner = testHelper.createUser("owner-id")
            val community = testHelper.createCommunity(
                ownerId = "owner-id",
                name = "Test Community",
                description = "Test Description",
                slug = "test-community-slug",
            )

            val result = underTest.execute(GetCommunity(null, "test-community-slug", "owner-id"))

            with(result) {
                assertThat(this.community).isEqualTo(community)
                assertThat(this.isMember).isTrue
            }
            assertThat(result.owner).isEqualTo(owner)
        }

        @Test
        fun `member flag is true for non owner member`() {
            val underTest = prepareService()

            val owner = testHelper.createUser("owner-id")
            val community = testHelper.createCommunity(
                ownerId = "owner-id",
                name = "Test Community",
                description = "Test Description",
                slug = "test-community-slug",
            )

            testHelper.createUser("member-id")
            testHelper.createCommunityMember(community.id, "member-id")

            val result = underTest.execute(GetCommunity(null, "test-community-slug", "member-id"))

            with(result) {
                assertThat(this.community).isEqualTo(community.copy(membersCount = 1))
                assertThat(this.isMember).isTrue
            }
            assertThat(result.owner).isEqualTo(owner)
        }

        @Test
        fun `member flag is false for non member`() {
            val underTest = prepareService()

            val owner = testHelper.createUser("owner-id")
            val community = testHelper.createCommunity(
                ownerId = "owner-id",
                name = "Test Community",
                description = "Test Description",
                slug = "test-community-slug",
            )

            val result = underTest.execute(GetCommunity(null, "test-community-slug", "non-member-id"))

            with(result) {
                assertThat(this.community).isEqualTo(community)
                assertThat(this.isMember).isFalse
            }
            assertThat(result.owner).isEqualTo(owner)
        }

        @Test
        fun `should throw NotFoundException when community does not exist by id`() {
            val underTest = prepareService()

            val nonExistentId = UUID.randomUUID()

            assertThatExceptionOfType(NotFoundException::class.java).isThrownBy {
                underTest.execute(GetCommunity(nonExistentId, null, null))
            }
        }

        @Test
        fun `should throw NotFoundException when community does not exist by slug`() {
            val underTest = prepareService()

            assertThatExceptionOfType(NotFoundException::class.java).isThrownBy {
                underTest.execute(GetCommunity(null, "non-existent-slug", null))
            }
        }
    }

    @Nested
    inner class GetCommunities {
        @Test
        fun `should get communities with correct isMember flag`() {
            val underTest = prepareService()

            val owner = testHelper.createUser("owner-id")
            val community = testHelper.createCommunity(ownerId = "owner-id")
            testHelper.createUser("member-id")
            testHelper.createCommunityMember(community.id, "member-id", state = CommunityMemberStatus.ACTIVE)

            val result = underTest.execute(GetCommunities("member-id", GetCommunitiesFilter("owner-id"), PageRequest()))

            assertThat(result.content).hasSize(1)
            assertThat(result.content.first().community).isEqualTo(community.copy(membersCount = 1))
            assertThat(result.content.first().owner).isEqualTo(owner)
            assertThat(result.content.first().isMember).isTrue
        }

        @Nested
        inner class Paging {
            @Test
            fun `should correctly page using after cursor`() {
                val underTest = prepareService()

                val now = Instant.ofEpochSecond(1000000)
                val owner = testHelper.createUser("owner-id")
                val community1 = testHelper.createCommunity(ownerId = "owner-id", createdAt = now - 5.seconds)
                val community2 = testHelper.createCommunity(ownerId = "owner-id", createdAt = now - 20.seconds)
                val community3 = testHelper.createCommunity(ownerId = "owner-id", createdAt = now - 10.seconds)

                val page1 = underTest.execute(GetCommunities(null, GetCommunitiesFilter(), PageRequest(pageSize = 1)))
                assertThat(page1.content).containsExactly(CommunityWithMeta(community1, false, owner))
                assertThat(page1.hasNext).isTrue

                val page2 = underTest.execute(GetCommunities(null, GetCommunitiesFilter(), page1.nextPageable))
                assertThat(page2.content).containsExactly(CommunityWithMeta(community3, false, owner))
                assertThat(page2.hasNext).isTrue

                val page3 = underTest.execute(GetCommunities(null, GetCommunitiesFilter(), page2.nextPageable))
                assertThat(page3.content).containsExactly(CommunityWithMeta(community2, false, owner))
                assertThat(page3.hasNext).isFalse
            }
        }

        @Nested
        inner class Filtering {
            @Nested
            inner class OwnerId {
                @Test
                fun `should get communities by owner id`() {
                    val underTest = prepareService()

                    val owner1 = testHelper.createUser("owner-id1")
                    val community1 = testHelper.createCommunity(ownerId = "owner-id1")

                    testHelper.createUser("owner-id2")
                    testHelper.createCommunity(ownerId = "owner-id2")

                    val filter = GetCommunitiesFilter("owner-id1")
                    val result = underTest.execute(GetCommunities(null, filter, PageRequest()))

                    assertThat(result.content).hasSize(1)
                    assertThat(result.content)
                        .containsExactly(CommunityWithMeta(community1, false, owner1))
                }
            }

            @Nested
            inner class IsMember {
                @Test
                fun `should get communities where user is a member`() {
                    val underTest = prepareService()

                    val owner = testHelper.createUser("owner-id")
                    testHelper.createCommunity(ownerId = "owner-id")
                    val community2 = testHelper.createCommunity(ownerId = "owner-id").copy(membersCount = 1)
                    testHelper.createUser("member-id")
                    testHelper.createCommunityMember(community2.id, "member-id", state = CommunityMemberStatus.ACTIVE)

                    val filter = GetCommunitiesFilter(isMember = true)
                    val result = underTest.execute(GetCommunities("member-id", filter, PageRequest()))

                    assertThat(result.content).hasSize(1)
                    assertThat(result.content).containsExactly(CommunityWithMeta(community2, true, owner))
                }
            }

            @Nested
            inner class Query {
                @Test
                fun `should get that match the query`() {
                    val underTest = prepareService()

                    val owner = testHelper.createUser("owner-id")
                    testHelper.createCommunity(ownerId = "owner-id", name = "Vse o fotbalu")
                    val community = testHelper.createCommunity(ownerId = "owner-id", name = "Vse o baseballu")

                    val filter = GetCommunitiesFilter(query = "baseball")
                    val result = underTest.execute(GetCommunities(null, filter, PageRequest()))

                    assertThat(result.content).hasSize(1)
                    assertThat(result.content).containsExactly(CommunityWithMeta(community, false, owner))
                }
            }
        }

        @Nested
        inner class Sorting {
            @Nested
            inner class ByThreadsCount {
                @Test
                fun `should sort communities by threads count`() {
                    val underTest = prepareService()

                    val owner1 = testHelper.createUser("owner-id-1")
                    val owner2 = testHelper.createUser("owner-id-2")

                    val community1 = testHelper.createCommunity(ownerId = "owner-id-1", threadsCount = 10)
                    val community2 = testHelper.createCommunity(ownerId = "owner-id-2", threadsCount = 5)
                    val community3 = testHelper.createCommunity(ownerId = "owner-id-1", threadsCount = 15)

                    val filter = GetCommunitiesFilter()
                    val pageRequest = PageRequest(sort = Sort.by(THREADS_COUNT, Sort.Direction.DESC), pageSize = 1)
                    val result1 = underTest.execute(GetCommunities(null, filter, pageRequest))

                    assertThat(result1.content).containsExactly(CommunityWithMeta(community3, false, owner1))

                    val result2 = underTest.execute(GetCommunities(null, filter, result1.nextPageable))

                    assertThat(result2.content).containsExactly(CommunityWithMeta(community1, false, owner1))

                    val result3 = underTest.execute(GetCommunities(null, filter, result2.nextPageable))

                    assertThat(result3.content).containsExactly(CommunityWithMeta(community2, false, owner2))
                }
            }

            @Nested
            inner class ByCreatedAt {
                @Test
                fun `should sort communities by created at`() {
                    val underTest = prepareService()

                    val owner1 = testHelper.createUser("owner-id-1")
                    val owner2 = testHelper.createUser("owner-id-2")

                    val now = Instant.ofEpochSecond(9999)
                    val community1 = testHelper.createCommunity(ownerId = "owner-id-1", createdAt = now - 10.seconds)
                    val community2 = testHelper.createCommunity(ownerId = "owner-id-2", createdAt = now - 15.seconds)
                    val community3 = testHelper.createCommunity(ownerId = "owner-id-1", createdAt = now - 5.seconds)

                    val filter = GetCommunitiesFilter()
                    val pageRequest = PageRequest(sort = Sort.by(CREATED_AT, Sort.Direction.DESC), pageSize = 1)
                    val result1 = underTest.execute(GetCommunities(null, filter, pageRequest))

                    assertThat(result1.content).containsExactly(CommunityWithMeta(community3, false, owner1))

                    val result2 = underTest.execute(GetCommunities(null, filter, result1.nextPageable))

                    assertThat(result2.content).containsExactly(CommunityWithMeta(community1, false, owner1))

                    val result3 = underTest.execute(GetCommunities(null, filter, result2.nextPageable))

                    assertThat(result3.content).containsExactly(CommunityWithMeta(community2, false, owner2))
                }
            }
        }
    }

    private fun prepareService(): CommunityQueryService =
        CommunityQueryService(
            lazyContext = lazyTestContext,
        )
}
