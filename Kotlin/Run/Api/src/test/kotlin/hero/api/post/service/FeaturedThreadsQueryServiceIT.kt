package hero.api.post.service

import hero.baseutils.minus
import hero.baseutils.plus
import hero.core.data.PageRequest
import hero.model.CommunityType
import hero.model.FREE_SUBSCRIBER_TIER_ID
import hero.test.IntegrationTest
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.Test
import java.time.Instant
import kotlin.time.Duration.Companion.seconds

class FeaturedThreadsQueryServiceIT : IntegrationTest() {
    @Nested
    inner class GetMostUpVotedThreads {
        @Test
        fun `should return popular threads that were published after since and are not in private community`() {
            val underTest = prepareService()

            val since = Instant.ofEpochSecond(9999)

            val community1 = testHelper.createCommunity("owner1")
            val community2 = testHelper.createCommunity("owner2")

            // threads from this community cannot be returned
            testHelper.createUser("owner3", tierId = FREE_SUBSCRIBER_TIER_ID)
            val community3 = testHelper.createCommunity("owner3", type = CommunityType.CONNECTED)

            val thread1 = testHelper.createPost(
                "filip",
                communityId = community1.id,
                publishedAt = since + 10.seconds,
                voteScore = 10,
            )
            val thread2 = testHelper.createPost(
                "holes",
                communityId = community2.id,
                publishedAt = since + 15.seconds,
                voteScore = 15,
            )

            // this thread should not be returned since it's old
            val thread3 = testHelper.createPost("pavel", communityId = community2.id, publishedAt = since - 5.seconds)
            val thread4 = testHelper.createPost("pavel", communityId = community3.id)

            val result = underTest.execute(GetMostUpVotedThreads(since, PageRequest()))

            assertThat(result.content)
                .containsExactly(thread2, thread1)
                .doesNotContain(thread3, thread4)
        }

        @Test
        fun `should correctly page`() {
            val underTest = prepareService()

            val since = Instant.ofEpochSecond(9999)

            val community1 = testHelper.createCommunity("owner1")
            val community2 = testHelper.createCommunity("owner2")

            val thread1 = testHelper.createPost(
                "filip",
                communityId = community1.id,
                publishedAt = since + 10.seconds,
                voteScore = 10,
            )
            val thread2 = testHelper.createPost(
                "holes",
                communityId = community2.id,
                publishedAt = since + 15.seconds,
                voteScore = 15,
            )

            val result1 = underTest.execute(GetMostUpVotedThreads(since, PageRequest(pageSize = 1)))

            assertThat(result1.content).containsExactly(thread2)
            assertThat(result1.hasNext).isTrue()

            val result2 = underTest.execute(GetMostUpVotedThreads(since, result1.nextPageable))

            assertThat(result2.content).containsExactly(thread1)
            assertThat(result2.hasNext).isFalse()
        }
    }

    private fun prepareService(): FeaturedThreadsQueryService = FeaturedThreadsQueryService(lazyTestContext)
}
