package hero.api.post.service

import hero.baseutils.minus
import hero.baseutils.minusHours
import hero.core.data.PageRequest
import hero.core.data.Sort
import hero.exceptions.http.ForbiddenException
import hero.exceptions.http.NotFoundException
import hero.model.CommunityType
import hero.model.FREE_SUBSCRIBER_TIER_ID
import hero.model.topics.PostState
import hero.test.IntegrationTest
import hero.test.IntegrationTestHelper.TestCollections.subscribersCollection
import hero.test.TestRepositories.postRepository
import org.assertj.core.api.Assertions.assertThat
import org.assertj.core.api.Assertions.assertThatExceptionOfType
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.Test
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.EnumSource
import java.time.Instant
import kotlin.time.Duration.Companion.seconds

class CommentQueryServiceIT : IntegrationTest() {
    @Nested
    inner class GetComments {
        @Test
        fun `should return comments for a top level post with subscription information for a subscriber`() {
            val underTest = CommentQueryService(lazyTestContext, postRepository, subscribersCollection)
            val post = testHelper.createPost("filip", state = PostState.PUBLISHED)

            val comment1 = testHelper.createPost("adrian", parentId = post.id, text = "comment1")
            val comment2 = testHelper.createPost("kamil", parentId = post.id, text = "comment2")

            val subscription = testHelper.createSubscriber("filip", "pepa")
            testHelper.createUser("pepa")

            val comments = underTest.execute(GetComments("pepa", post.id, PageRequest()))

            assertThat(comments.content).hasSize(2)
            assertThat(comments.hasNext).isFalse()

            assertThat(comments.content).filteredOn { it.comment.id == comment1.id }
                .hasSize(1)
                .allMatch {
                    it.comment.text == "comment1" &&
                        it.subscriptionInfo == subscription &&
                        it.postAuthor == "filip" &&
                        it.myVote == 0 &&
                        it.isPartOfCommunity == null
                }

            assertThat(comments.content).filteredOn { it.comment.id == comment2.id }
                .hasSize(1)
                .allMatch {
                    it.comment.text == "comment2" &&
                        it.subscriptionInfo == subscription &&
                        it.postAuthor == "filip" &&
                        it.myVote == 0 &&
                        it.isPartOfCommunity == null
                }
        }

        @Test
        fun `should return comments for a thread with my vote`() {
            val underTest = CommentQueryService(lazyTestContext, postRepository, subscribersCollection)
            testHelper.createUser("cestmir")
            val community = testHelper.createCommunity("cestmir")
            testHelper.createCommunityMember(community.id, "pepa")

            val thread = testHelper.createPost("cestmir", state = PostState.PUBLISHED, communityId = community.id)

            val comment1 = testHelper.createPost("adrian", parentId = thread.id, text = "comment1")
            val comment2 = testHelper.createPost("kamil", parentId = thread.id, text = "comment2")

            testHelper.createUser("pepa")
            testHelper.createPostVote("pepa", comment1.id, 1)
            testHelper.createPostVote("pepa", comment2.id, -1)

            val comments = underTest.execute(GetComments("pepa", thread.id, PageRequest()))

            assertThat(comments.content).hasSize(2)
            assertThat(comments.hasNext).isFalse()

            assertThat(comments.content).filteredOn { it.comment.id == comment1.id }
                .hasSize(1)
                .allMatch {
                    it.comment.text == "comment1" &&
                        it.subscriptionInfo == null &&
                        it.postAuthor == "cestmir" &&
                        it.myVote == 1 &&
                        it.isPartOfCommunity == true
                }

            assertThat(comments.content).filteredOn { it.comment.id == comment2.id }
                .hasSize(1)
                .allMatch {
                    it.comment.text == "comment2" &&
                        it.subscriptionInfo == null &&
                        it.postAuthor == "cestmir" &&
                        it.myVote == -1 &&
                        it.isPartOfCommunity == true
                }
        }

        @Test
        fun `should sort by given sort direction`() {
            val underTest = CommentQueryService(lazyTestContext, postRepository, subscribersCollection)
            val post = testHelper.createPost("filip", state = PostState.PUBLISHED)

            val now = Instant.now()
            val comment1 = testHelper.createPost(
                "adrian",
                parentId = post.id,
                text = "comment1",
                publishedAt = now - 5.seconds,
            )
            val comment2 = testHelper.createPost(
                "kamil",
                parentId = post.id,
                text = "comment2",
                publishedAt = now - 10.seconds,
            )

            val subscription = testHelper.createSubscriber("filip", "pepa")

            val ascPageRequest = PageRequest(sort = Sort(direction = Sort.Direction.ASC))
            val ascComments = underTest.execute(GetComments("pepa", post.id, ascPageRequest))

            assertThat(ascComments.content).containsExactly(
                CommentWithMeta(post, false, comment2, subscription, "filip", 0, null),
                CommentWithMeta(post, false, comment1, subscription, "filip", 0, null),
            )

            val descPageRequest = PageRequest(sort = Sort(direction = Sort.Direction.DESC))
            val descComments = underTest.execute(GetComments("pepa", post.id, descPageRequest))

            assertThat(descComments.content).containsExactly(
                CommentWithMeta(post, false, comment1, subscription, "filip", 0, null),
                CommentWithMeta(post, false, comment2, subscription, "filip", 0, null),
            )
        }

        @Test
        fun `should return nested comments for a comment with subscription information for a subscriber`() {
            val underTest = CommentQueryService(lazyTestContext, postRepository, subscribersCollection)
            val post = testHelper.createPost("filip", state = PostState.PUBLISHED)

            val comment = testHelper.createPost("martin", parentId = post.id, text = "top-level-comment")
            val nestedComment1 = testHelper.createPost("adrian", parentId = comment.id, text = "nested1")
            val nestedComment2 = testHelper.createPost("kamil", parentId = comment.id, text = "nested2")

            val subscription = testHelper.createSubscriber("filip", "pepa")

            val comments = underTest.execute(GetComments("pepa", comment.id, PageRequest()))

            assertThat(comments.content).hasSize(2)
            assertThat(comments.hasNext).isFalse()

            assertThat(comments.content).filteredOn { it.comment.id == nestedComment1.id }
                .hasSize(1)
                .allMatch {
                    it.comment.text == "nested1" && it.subscriptionInfo == subscription && it.postAuthor == "filip"
                }

            assertThat(comments.content).filteredOn { it.comment.id == nestedComment2.id }
                .hasSize(1)
                .allMatch {
                    it.comment.text == "nested2" && it.subscriptionInfo == subscription && it.postAuthor == "filip"
                }
        }

        @ParameterizedTest
        @EnumSource(PostState::class, names = ["SCHEDULED", "PROCESSING", "PUBLISHED"])
        fun `should return comments for the post creator if the post is in all allowed states`(postState: PostState) {
            val underTest = CommentQueryService(lazyTestContext, postRepository, subscribersCollection)
            val post = testHelper.createPost("filip", state = postState)

            val comment1 = testHelper.createPost("adrian", parentId = post.id, text = "comment1")
            val comment2 = testHelper.createPost("kamil", parentId = post.id, text = "comment2")

            val comments = underTest.execute(GetComments("filip", post.id, PageRequest()))

            assertThat(comments.content).hasSize(2)
            assertThat(comments.hasNext).isFalse()

            assertThat(comments.content).filteredOn { it.comment.id == comment1.id }
                .hasSize(1)
                .allMatch { it.comment.text == "comment1" && it.subscriptionInfo == null && it.postAuthor == "filip" }

            assertThat(comments.content).filteredOn { it.comment.id == comment2.id }
                .hasSize(1)
                .allMatch { it.comment.text == "comment2" && it.subscriptionInfo == null && it.postAuthor == "filip" }
        }

        @Test
        fun `should paginate using after cursor and order by publishedAt descending`() {
            val underTest = CommentQueryService(lazyTestContext, postRepository, subscribersCollection)
            val post = testHelper.createPost("filip", state = PostState.PUBLISHED)

            val now = Instant.now()
            val comment1 = testHelper.createPost("adrian", parentId = post.id, publishedAt = now)
            val comment2 = testHelper.createPost("kamil", parentId = post.id, publishedAt = now - 5.seconds)
            val comment3 = testHelper.createPost("olda", parentId = post.id, publishedAt = now - 10.seconds)

            val requesterSubscription = testHelper.createSubscriber("filip", "pepa")

            val firstComment = underTest.execute(GetComments("pepa", post.id, PageRequest(pageSize = 1)))

            assertThat(firstComment.content)
                .containsExactly(CommentWithMeta(post, false, comment1, requesterSubscription, "filip", 0, null))
            assertThat(firstComment.hasNext).isTrue()

            val secondPageRequest = PageRequest(pageSize = 1, afterCursor = firstComment.nextPageable.afterCursor)
            val secondComment = underTest.execute(GetComments("pepa", post.id, secondPageRequest))

            assertThat(secondComment.content)
                .containsExactly(CommentWithMeta(post, false, comment2, requesterSubscription, "filip", 0, null))
            assertThat(secondComment.hasNext).isTrue()

            val thirdPageRequest = PageRequest(pageSize = 1, afterCursor = secondComment.nextPageable.afterCursor)
            val thirdComment = underTest.execute(GetComments("pepa", post.id, thirdPageRequest))

            assertThat(thirdComment.content)
                .containsExactly(CommentWithMeta(post, false, comment3, requesterSubscription, "filip", 0, null))
            assertThat(thirdComment.hasNext).isFalse()

            val beforePageRequest = PageRequest(pageSize = 2, beforeCursor = thirdComment.nextPageable.beforeCursor)
            val firstTwoComments = underTest.execute(GetComments("pepa", post.id, beforePageRequest))
            assertThat(firstTwoComments.content)
                .containsExactly(
                    CommentWithMeta(post, false, comment1, requesterSubscription, "filip", 0, null),
                    CommentWithMeta(post, false, comment2, requesterSubscription, "filip", 0, null),
                )
            assertThat(firstTwoComments.hasNext).isFalse()
        }

        @Test
        fun `should not return any comments if a user is not subscriber of the post creator and no post previews`() {
            val underTest = CommentQueryService(lazyTestContext, postRepository, subscribersCollection)
            testHelper.createUser("filip", hasPostPreviews = false)
            val post = testHelper.createPost("filip", state = PostState.PUBLISHED)

            testHelper.createPost("adrian", parentId = post.id, text = "comment1")
            testHelper.createPost("kamil", parentId = post.id, text = "comment2")

            val comments = underTest.execute(GetComments("pavel", post.id, PageRequest()))

            assertThat(comments.content).isEmpty()
            assertThat(comments.hasNext).isFalse()
        }

        @Test
        fun `should return comments if creator has post previews and the root post has post previews enabled`() {
            val underTest = CommentQueryService(lazyTestContext, postRepository, subscribersCollection)
            testHelper.createUser("filip", hasPostPreviews = true)
            val post = testHelper.createPost("filip", state = PostState.PUBLISHED, hasPreview = true)

            val now = Instant.now()
            val before = now.minusHours(1)
            val comment1 = testHelper.createPost("adrian", parentId = post.id, text = "comment1", publishedAt = now)
            val comment2 = testHelper.createPost("kamil", parentId = post.id, text = "comment2", publishedAt = before)

            val comments = underTest.execute(GetComments("pavel", post.id, PageRequest()))

            assertThat(comments.content)
                .containsExactly(
                    CommentWithMeta(post, true, comment1, null, "filip", 0, null),
                    CommentWithMeta(post, true, comment2, null, "filip", 0, null),
                )
        }

        @Test
        fun `should not return comments to a thread if user is not part of community and owner has private profile`() {
            val underTest = CommentQueryService(lazyTestContext, postRepository, subscribersCollection)
            testHelper.createUser("cestmir", tierId = FREE_SUBSCRIBER_TIER_ID)
            val community = testHelper.createCommunity("cestmir", type = CommunityType.CONNECTED)

            testHelper.createUser("filip")
            val thread = testHelper.createPost("filip", state = PostState.PUBLISHED, communityId = community.id)

            testHelper.createPost("adrian", parentId = thread.id, text = "comment1")
            testHelper.createPost("kamil", parentId = thread.id, text = "comment2")

            val comments = underTest.execute(GetComments("pavel", thread.id, PageRequest()))

            assertThat(comments.content).isEmpty()
            assertThat(comments.hasNext).isFalse()
        }

        @ParameterizedTest
        @EnumSource(PostState::class, names = ["SCHEDULED", "PROCESSING"])
        fun `should throw forbidden if post is processing, scheduled and user is not the owner`(postState: PostState) {
            val underTest = CommentQueryService(lazyTestContext, postRepository, subscribersCollection)
            val post = testHelper.createPost("filip", state = postState)

            assertThatExceptionOfType(ForbiddenException::class.java).isThrownBy {
                underTest.execute(GetComments("pavel", post.id, PageRequest()))
            }
        }

        @ParameterizedTest
        @EnumSource(PostState::class, names = ["DELETED", "REVISION"])
        fun `should throw not found if post is deleted or is a revision`(postState: PostState) {
            val underTest = CommentQueryService(lazyTestContext, postRepository, subscribersCollection)
            val post = testHelper.createPost("filip", state = postState)

            assertThatExceptionOfType(NotFoundException::class.java).isThrownBy {
                underTest.execute(GetComments("pavel", post.id, PageRequest()))
            }
        }
    }

    @Nested
    inner class GetComment {
        @Test
        fun `should fetch a reply comment, its parent and root parent, and my vote`() {
            val underTest = CommentQueryService(lazyTestContext, postRepository, subscribersCollection)
            val post = testHelper.createPost("cestmir")
            val comment = testHelper.createPost("filip", parentId = post.id)
            val replyComment = testHelper.createPost("petr", parentId = comment.id)
            val siblingComment = testHelper.createPost("ondra", parentId = comment.id, siblingId = replyComment.id)

            val result = underTest.execute(GetComment(siblingComment.id, "filip"))

            assertThat(result)
                .isInstanceOf(ReplyData::class.java)
                .isEqualTo(
                    ReplyData(
                        comment = siblingComment,
                        rootPost = post,
                        subscriptionInfo = null,
                        parent = comment,
                        myVote = 0,
                        isPartOfCommunity = null,
                    ),
                )
        }

        @Test
        fun `should fetch comment and its post parent and subscription info`() {
            val underTest = CommentQueryService(lazyTestContext, postRepository, subscribersCollection)
            val post = testHelper.createPost("cestmir")
            val comment = testHelper.createPost("filip", parentId = post.id)
            val subscription = testHelper.createSubscriber("cestmir", "filip")

            val result = underTest.execute(GetComment(comment.id, "filip"))

            assertThat(result)
                .isInstanceOf(CommentData::class.java)
                .isEqualTo(
                    CommentData(
                        comment = comment,
                        rootPost = post,
                        subscriptionInfo = subscription,
                        myVote = 0,
                        null,
                    ),
                )
        }
    }
}
