package hero.api.subscriber.service

import hero.baseutils.fromBase64
import hero.baseutils.toBase64
import hero.core.data.Page
import hero.core.data.PageRequest
import hero.core.data.Pageable
import hero.jackson.fromJson
import hero.jackson.toJson
import hero.model.SubscribeRequest
import hero.repository.subscription.JooqSubscribeRequestHelper
import hero.sql.jooq.Tables.SUBSCRIBE_REQUEST
import org.jooq.DSLContext
import org.jooq.Record
import org.jooq.SelectConditionStep
import java.time.Instant

class SubscribeRequestQueryService(
    lazyContext: Lazy<DSLContext>,
) {
    private val context by lazyContext

    fun execute(query: FindLastSubscribeRequest): SubscribeRequest? =
        context
            .select(JooqSubscribeRequestHelper.subscribeRequestFields)
            .from(SUBSCRIBE_REQUEST)
            .where(SUBSCRIBE_REQUEST.CREATOR_ID.eq(query.creatorId))
            .and(SUBSCRIBE_REQUEST.USER_ID.eq(query.userId))
            .orderBy(SUBSCRIBE_REQUEST.CREATED_AT.desc())
            .limit(1)
            .fetchOne()
            ?.map { JooqSubscribeRequestHelper.mapRecordToEntity(it) }

    fun execute(query: GetSubscribeRequests): Page<SubscribeRequest> {
        val afterCursor = query.pageable.afterCursor?.fromBase64()?.fromJson<GetSubscribeRequestsCursor>()

        val (requests, hasNext) = context
            .select(JooqSubscribeRequestHelper.subscribeRequestFields)
            .from(SUBSCRIBE_REQUEST)
            .where(SUBSCRIBE_REQUEST.CREATOR_ID.eq(query.userId))
            .and(SUBSCRIBE_REQUEST.ACCEPTED_AT.isNull)
            .and(SUBSCRIBE_REQUEST.DECLINED_AT.isNull)
            .and(SUBSCRIBE_REQUEST.DELETED_AT.isNull)
            .editByCursor(afterCursor)
            .orderBy(SUBSCRIBE_REQUEST.CREATED_AT.desc())
            .limit(query.pageable.pageSize + 1)
            .fetch()
            .map { JooqSubscribeRequestHelper.mapRecordToEntity(it) }
            .let {
                it.take(query.pageable.pageSize) to (it.size > query.pageable.pageSize)
            }

        return Page(requests, nextPageable(requests, query.pageable), hasNext)
    }

    private fun <T : Record> SelectConditionStep<T>.editByCursor(afterCursor: GetSubscribeRequestsCursor?) =
        when {
            afterCursor != null -> this.and(SUBSCRIBE_REQUEST.CREATED_AT.lt(afterCursor.lastCreatedAt))
            else -> this
        }

    private fun nextPageable(
        requests: List<SubscribeRequest>,
        pageable: Pageable,
    ): PageRequest {
        val afterCursor = requests
            .lastOrNull()
            ?.let {
                GetSubscribeRequestsCursor(it.createdAt).toJson().toBase64()
            }

        return PageRequest(-1, pageable.pageSize, afterCursor = afterCursor)
    }
}

data class GetSubscribeRequests(
    val userId: String,
    val pageable: Pageable,
)

data class GetSubscribeRequestsCursor(
    val lastCreatedAt: Instant,
)

data class FindLastSubscribeRequest(
    val creatorId: String,
    val userId: String,
)
