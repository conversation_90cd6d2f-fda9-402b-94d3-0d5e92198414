package hero.api.post.controller.dto

import hero.api.post.service.CreatorPostsSortingFields
import hero.api.post.service.GetCreatorPostsFilter
import hero.api.post.service.PostFilterType
import hero.api.post.service.PostSource
import hero.api.post.service.dto.PostInput
import hero.contract.api.dto.CategoryResponse
import hero.contract.api.dto.GjirafaAssetInput
import hero.contract.api.dto.GjirafaLivestreamAssetInput
import hero.contract.api.dto.ImageAssetInput
import hero.contract.api.dto.PagedPostResponse
import hero.contract.api.dto.PostAssetInput
import hero.contract.api.dto.PostAssetResponse
import hero.contract.api.dto.PostPreviewAssetResponse
import hero.contract.api.dto.PostRelationships
import hero.contract.api.dto.PostResponse
import hero.contract.api.dto.PreviewDocumentResponse
import hero.contract.api.dto.PreviewGjirafaLivestreamResponse
import hero.contract.api.dto.PreviewGjirafaResponse
import hero.contract.api.dto.PreviewGjirafaType
import hero.contract.api.dto.PreviewImageResponse
import hero.contract.api.dto.SavedCreatorPostInfoResponse
import hero.core.data.Sort
import hero.gjirafa.dto.exampleGjirafaAsset
import hero.gjirafa.dto.exampleGjirafaLiveAsset
import hero.model.Chapter
import hero.model.DocumentAsset
import hero.model.DocumentType
import hero.model.ImageAssetDto
import hero.model.PostCounts
import hero.model.YouTubeAsset
import hero.model.topics.PostState
import java.time.Instant
import java.util.UUID

val examplePostResponse = PostResponse(
    id = "1683706670998-1431998399-1693903390616-axgzjkxarblzbkqftia",
    state = PostState.DELETED,
    counts = PostCounts(
        comments = 1,
        replies = 2,
    ),
    assets = listOf(
        PostAssetResponse(
            image = ImageAssetDto("https://uploaded/image/url", 640, 480, "file.jpg", 100),
            youTube = YouTubeAsset("5laRP4DP1Sc", 640, 480, "https://thumbnail.jpg"),
            gjirafa = exampleGjirafaAsset,
            gjirafaLive = exampleGjirafaLiveAsset,
            document = DocumentAsset("https://uploaded/image/url", DocumentType.PDF, "super.pdf", 100),
            thumbnail = "https://uploaded/image/url",
            bunnyAsset = "https://bunny-asset",
            audioAsset = "https://audio-asset.mp3",
            thumbnailImage = ImageAssetDto("https://uploaded/image/url", 640, 480, "file.jpg", 100),
            timestamp = 100.0,
        ),
    ),
    previewAssets = listOf(
        PostPreviewAssetResponse(
            thumbnailImage = ImageAssetDto("https://uploaded/image/url", 640, 480, "file.jpg", 100),
            image = PreviewImageResponse("https://uploaded/image/url", 640, 480),
            gjirafa = PreviewGjirafaResponse(
                previewStaticUrl = "https://preview-static-url",
                previewAnimatedUrl = "https://preview-animated-url",
                previewStripUrl = "https://preview-strip-url",
                type = PreviewGjirafaType.AUDIO,
                duration = 100.0,
                width = 100,
                height = 50,
            ),
            document = PreviewDocumentResponse(
                type = DocumentType.PDF,
                name = "document-name",
            ),
            gjirafaLive = PreviewGjirafaLivestreamResponse(
                thumbnailImage = ImageAssetDto("https://uploaded/image/url", 640, 480, "file.jpg", 100),
            ),
        ),
    ),
    assetsCount = 1,
    price = 10,
    fullAsset = false,
    pinnedAt = Instant.now(),
    publishedAt = Instant.now(),
    title = "title",
    text = "text",
    textHtml = "<h1>text<h1>",
    excludeFromRss = false,
    textDelta = "text-delta",
    categories = listOf(
        CategoryResponse(
            id = "category-id",
            name = "category",
            slug = "category-slug",
        ),
    ),
    savedPostInfo = SavedCreatorPostInfoResponse("userId-postId", Instant.now()),
    relationships = PostRelationships(
        userId = "user1",
        messageThreadId = "1683706670998-1431998399",
        parentId = "parent-id",
        siblingId = "sibling-id",
    ),
    chapters = listOf(
        Chapter("first", 13),
        Chapter("second", 200),
    ),
    isAgeRestricted = true,
    isSponsored = true,
    pollId = "poll-id",
    myVote = 1,
    voteScore = 10,
    hasPreview = true,
    hasPreviewInternal = true,
)

val exampleCommentResponse = CommentResponse(examplePostResponse, examplePostResponse, examplePostResponse)

val examplePaginatedPostResponse = PagedPostResponse(
    content = listOf(examplePostResponse),
    hasNext = false,
    afterCursor = "eyJsYXN0UHVibGlzaGVkQXQiOiIyMDIzLTA5LTA1VDA4OjQzOjEwLjYxNjQzMVoifQ==",
    beforeCursor = "eyJsYXN0UHVibGlzaGVkQXQiOiIyMDIzLTA5LTA1VDA4OjQzOjEwLjYxNjQzMVoifQ==",
)

val exampleSearchPostsRequest = SearchPostsRequest(
    creatorId = "creator-id",
    afterCursor = "after-cursor",
    beforeCursor = "before-cursor",
    pageSize = 10,
    filter = GetCreatorPostsFilter(
        type = PostFilterType.IN_PROGRESS,
        categoryId = "category-id",
        query = "query",
        communityId = UUID.randomUUID(),
        excludedCreatorIds = listOf("creator-id"),
        source = PostSource.SUBSCRIBED_CREATORS,
    ),
    sortBy = CreatorPostsSortingFields.WATCHED_AT,
    sortDirection = Sort.Direction.DESC,
)

val examplePostAssetInput =
    PostAssetInput(
        image = ImageAssetInput(
            url = "url",
            width = 100,
            height = 500,
            fileName = "image.jpg",
            fileSize = 100L,
            hidden = false,
        ),
        gjirafa = GjirafaAssetInput(id = "vjsnqrol"),
        gjirafaLivestream = GjirafaLivestreamAssetInput("vjsnvehf"),
        document = DocumentAsset(
            url = "url-document",
            type = DocumentType.PDF,
            name = "document-name",
            fileSize = 100,
        ),
        thumbnail = "thumbnail",
        thumbnailImage = ImageAssetInput(
            url = "url",
            width = 100,
            height = 500,
            fileName = "image.jpg",
            fileSize = 100L,
            hidden = false,
        ),
        youtube = YouTubeAsset("5laRP4DP1Sc", 640, 480, "https://thumbnail.jpg"),
    )

private val examplePostInput = PostInput(
    title = "title",
    text = "text",
    textHtml = "<h1>cus</h1>",
    textDelta = "{\"ops\":[{\"insert\":\"dobrej komentar\\n\"}]}",
    assets = listOf(examplePostAssetInput),
)

val exampleCreateCommentRequest = CreateCommentRequest(
    parentId = "parent-id",
    siblingId = "sibling-id",
    attributes = examplePostInput,
)

val exampleCreatePostRequest = CreatePostRequest(
    publishedAt = Instant.now(),
    categories = setOf("sports"),
    attributes = examplePostInput,
    isSponsored = true,
    isAgeRestricted = false,
    communityId = UUID.randomUUID(),
    hasPreview = false,
)

val exampleUpdatePostRequest = UpdatePostRequest(
    publishedAt = Instant.now(),
    pinnedAt = Instant.now(),
    categories = setOf("sports"),
    attributes = examplePostInput,
    isSponsored = true,
    excludeFromRss = false,
    isAgeRestricted = false,
    hasPreview = false,
)

val examplePostVoteRequest = PostVoteRequest(1)

val exampleUpdateCommentRequest = UpdateCommentRequest(
    attributes = examplePostInput,
)

val examplePollResponse = PollResponse(
    id = "poll-id",
    deadline = Instant.now(),
    options = listOf(
        PollOptionResponse(
            id = "option-id",
            title = "Option 1",
            voteCount = 10,
            hasVotedFor = false,
        ),
        PollOptionResponse(
            id = "option-id-2",
            title = "Option 2",
            voteCount = 0,
            hasVotedFor = true,
        ),
    ),
)

val exampleCastVotesRequest = CastVotesRequest(listOf(VoteRequest("option-id")))

val exampleUpdatePollRequest = UpdatePollRequest(true)
