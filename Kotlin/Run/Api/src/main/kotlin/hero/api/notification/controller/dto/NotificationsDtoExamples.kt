package hero.api.notification.controller.dto

import hero.api.user.controller.dto.exampleUserResponse
import hero.model.NotificationType
import hero.model.StorageEntityType
import java.time.Instant
import java.util.UUID

val exampleUpdateNotificationInput = UpdateNotificationInput(
    checkedAt = Instant.now(),
    seenAt = Instant.now(),
)

val exampleNotificationResponse = NotificationResponse(
    id = "notification-id",
    type = NotificationType.NEW_POST,
    createdAt = Instant.now(),
    checkedAt = Instant.now(),
    seenAt = Instant.now(),
    actorCount = 10,
    lastActor = exampleUserResponse,
    relationships = NotificationRelationships(
        objectId = "object-id",
        objectType = StorageEntityType.USER,
        lastActorId = "last-actor-id",
    ),
    communityId = UUID.randomUUID(),
)

val examplePagedNotificationResponse = PagedNotificationResponse(
    content = listOf(exampleNotificationResponse),
    hasNext = true,
    afterCursor = "eyJsYXN0TWVzc2FnZUF0IjoiMjAyMy0wOS0wNVQwODo0MzoxMC42MTY0MzFaIn0=",
    beforeCursor = "eyJsYXN0TWVzc2FnZUF0IjoiMjAyMy0wOS0wNVQwODo0MzoxMC42MTY0MzFaIn0=",
)

val exampleNotificationSettingsResponse = NotificationSettingsResponse(
    emailNewPost = true,
    emailNewDm = true,
    pushNewPost = true,
    pushNewComment = true,
    pushNewMessage = true,
    newsletter = true,
    termsChanged = true,
)

val exampleNotificationSettingsUpdateRequest = NotificationSettingsUpdateRequest(
    emailNewPost = true,
    emailNewDm = true,
    pushNewPost = true,
    pushNewComment = true,
    pushNewMessage = true,
    newsletter = true,
    termsChanged = true,
)
