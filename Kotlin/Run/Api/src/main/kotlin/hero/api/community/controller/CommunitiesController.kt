package hero.api.community.controller

import hero.api.community.controller.dto.UpdateCommunityRequest
import hero.api.community.controller.dto.exampleCommunityResponse
import hero.api.community.controller.dto.examplePagedCommunityResponse
import hero.api.community.controller.dto.exampleUpdateCommunitRequest
import hero.api.community.controller.dto.toResponse
import hero.api.community.service.CommunitiesSortingFields
import hero.api.community.service.CommunityCommandService
import hero.api.community.service.CommunityQueryService
import hero.api.community.service.CreateCommunity
import hero.api.community.service.GetCommunities
import hero.api.community.service.GetCommunitiesFilter
import hero.api.community.service.GetCommunity
import hero.api.community.service.JoinCommunity
import hero.api.community.service.LeaveCommunity
import hero.api.community.service.UpdateCommunity
import hero.api.user.service.GetUser
import hero.api.user.service.UserQueryService
import hero.core.data.PageRequest
import hero.core.data.Sort
import hero.core.data.toResponse
import hero.http4k.auth.getJwtUser
import hero.http4k.auth.parseJwtUser
import hero.http4k.controller.QueryUtils
import hero.http4k.extensions.authorization
import hero.http4k.extensions.body
import hero.http4k.extensions.delete
import hero.http4k.extensions.enum
import hero.http4k.extensions.example
import hero.http4k.extensions.get
import hero.http4k.extensions.lens
import hero.http4k.extensions.post
import hero.http4k.extensions.put
import org.http4k.contract.ContractRoute
import org.http4k.contract.div
import org.http4k.core.Response
import org.http4k.core.Status
import org.http4k.lens.Header
import org.http4k.lens.Path
import org.http4k.lens.Query
import org.http4k.lens.boolean
import org.http4k.lens.string
import org.http4k.lens.uuid
import java.util.UUID

class CommunitiesController(
    private val communityCommandService: CommunityCommandService,
    private val communityQueryService: CommunityQueryService,
    private val userQueryService: UserQueryService,
) {
    @Suppress("unused")
    val routePostCommunities: ContractRoute =
        ("/v1/communities").post(
            summary = "Create new community.",
            tag = "Communities",
            parameters = object {
                val authorization = Header.authorization()
            },
            receiving = Unit,
            responses = listOf(Status.OK example exampleCommunityResponse),
            handler = { request, _ ->
                val jwtUser = request.getJwtUser()

                val community = communityCommandService.execute(CreateCommunity(jwtUser.id))
                val user = userQueryService.execute(GetUser(jwtUser.id))

                Response(Status.OK).body(community.toResponse(user.user, true))
            },
        )

    @Suppress("unused")
    val routeGetCommunity: ContractRoute =
        ("/v1/communities" / Path.string().of("communityId or slug")).get(
            summary = "Get community by id or slug",
            tag = "Communities",
            parameters = object {
                val isSlug = Query.boolean().defaulted("isSlug", false, "Is the provided id a slug.")
            },
            responses = listOf(Status.OK example exampleCommunityResponse),
            handler = { request, parameters, id ->
                val jwtUser = request.parseJwtUser()
                val isSlug = parameters.isSlug(request)
                val (communityId, slug) = if (isSlug) {
                    null to id
                } else {
                    UUID.fromString(id) to null
                }
                val result = communityQueryService.execute(GetCommunity(communityId, slug, jwtUser?.id))

                Response(Status.OK).body(result.toResponse())
            },
        )

    @Suppress("unused")
    val routeUpdateCommunities: ContractRoute =
        ("/v1/communities" / Path.string().of("communityId")).put(
            summary = "Update community",
            tag = "Communities",
            parameters = object {
                val authorization = Header.authorization()
            },
            receiving = exampleUpdateCommunitRequest,
            responses = listOf(Status.OK example exampleCommunityResponse),
            handler = { request, parameters, id ->
                val jwtUser = request.getJwtUser()
                val body = lens<UpdateCommunityRequest>()(request)
                val user = userQueryService.execute(GetUser(jwtUser.id))

                val result = communityCommandService.execute(
                    UpdateCommunity(
                        communityId = UUID.fromString(id),
                        userId = jwtUser.id,
                        name = body.name,
                        description = body.description,
                        image = body.image,
                        slug = body.slug,
                        type = body.type,
                    ),
                )

                Response(Status.OK).body(result.toResponse(user.user, true))
            },
        )

    @Suppress("unused")
    val routeOwnerCommunities: ContractRoute =
        ("/v1/users" / Path.string().of("userId") / "communities").get(
            summary = "Get owner's communities",
            tag = "Communities",
            parameters = object {
            },
            responses = listOf(Status.OK example examplePagedCommunityResponse),
            handler = { request, parameters, ownerId, _ ->
                val jwtUser = request.parseJwtUser()

                val filter = GetCommunitiesFilter(ownerId = ownerId)
                val result = communityQueryService.execute(GetCommunities(jwtUser?.id, filter))

                Response(Status.OK).body(result.toResponse { it.toResponse() })
            },
        )

    @Suppress("unused")
    val routeGetCommunities: ContractRoute =
        ("/v1/communities").get(
            summary = "Get communities and filter them",
            tag = "Communities",
            parameters = object {
                val ownerId = Query.string().optional("ownerId")
                val isMember = Query.boolean().optional("isMember")
                val query = Query.string().optional("query")
                val pageSize = QueryUtils.pageSize()
                val afterCursor = QueryUtils.afterCursor()
                val sortDirection = QueryUtils.sortDirection()
                val sortBy = Query.enum<CommunitiesSortingFields>()
                    .optional("by", "Field to sort by, default is created_at")
            },
            responses = listOf(Status.OK example examplePagedCommunityResponse),
            handler = { request, parameters ->
                val jwtUser = request.parseJwtUser()
                val ownerId = parameters.ownerId(request)
                val isMember = parameters.isMember(request)
                val query = parameters.query(request)
                val sortDirection = parameters.sortDirection(request)
                val sortBy = parameters.sortBy(request)
                val pageSize = parameters.pageSize(request)
                val afterCursor = parameters.afterCursor(request)

                val sort = Sort.by(sortBy, sortDirection)
                val filter = GetCommunitiesFilter(ownerId = ownerId, isMember = isMember, query = query)
                val pageable = PageRequest(pageSize = pageSize, afterCursor = afterCursor, sort = sort)
                val result = communityQueryService.execute(GetCommunities(jwtUser?.id, filter, pageable))

                Response(Status.OK).body(result.toResponse { it.toResponse() })
            },
        )

    @Suppress("unused")
    val routePostCommunityMembers: ContractRoute =
        ("/v1/communities" / Path.uuid().of("communityId") / "members").post(
            summary = "Join community",
            tag = "Communities",
            parameters = object {},
            receiving = Unit,
            responses = listOf(Status.NO_CONTENT to Unit),
            handler = { request, parameters, communityId, _ ->
                val jwtUser = request.getJwtUser()

                communityCommandService.execute(JoinCommunity(jwtUser.id, communityId))

                Response(Status.NO_CONTENT)
            },
        )

    @Suppress("unused")
    val routeDeleteCommunityMembers: ContractRoute =
        ("/v1/communities" / Path.uuid().of("communityId") / "members" / Path.string().of("userId")).delete(
            summary = "Leave or remove member from community",
            tag = "Communities",
            parameters = object {},
            responses = listOf(Status.NO_CONTENT to Unit),
            handler = { request, parameters, communityId, _, userId ->
                val jwtUser = request.getJwtUser()
                require(jwtUser.id == userId) { "Authenticated user id must be same as the path user id" }

                communityCommandService.execute(LeaveCommunity(jwtUser.id, communityId))

                Response(Status.NO_CONTENT)
            },
        )
}
