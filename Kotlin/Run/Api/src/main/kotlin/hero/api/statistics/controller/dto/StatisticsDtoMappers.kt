package hero.api.statistics.controller.dto

import hero.api.post.controller.dto.PostRenderMeta
import hero.api.post.controller.dto.toResponse
import hero.api.statistics.service.PostWithCompleteStats
import hero.api.statistics.service.PostWithViewStats

fun PostWithViewStats.toDto(): PostWithViewStatsResponse =
    PostWithViewStatsResponse(
        post = post.toResponse(PostRenderMeta(true), 0),
        stats = stats,
    )

fun PostWithCompleteStats.toDto(): PostWithCompleteStatsResponse =
    PostWithCompleteStatsResponse(
        post = post.toResponse(PostRenderMeta(true), 0),
        stats = stats,
    )
