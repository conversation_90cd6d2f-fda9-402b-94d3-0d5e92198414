package hero.api.post.service

import hero.baseutils.fromBase64
import hero.baseutils.toBase64
import hero.core.data.Page
import hero.core.data.PageRequest
import hero.core.data.Pageable
import hero.exceptions.http.BadRequestException
import hero.exceptions.http.NotFoundException
import hero.gcloud.TypedCollectionReference
import hero.jackson.fromJson
import hero.jackson.toJson
import hero.model.CommunityType.CONNECTED
import hero.model.Creator
import hero.model.FREE_SUBSCRIBER_TIER_ID
import hero.model.Post
import hero.model.Subscriber
import hero.model.topics.PostState
import hero.repository.community.fetchMemberCommunityIds
import hero.repository.post.JooqPostHelper
import hero.repository.post.PostRepository
import hero.repository.post.PostType
import hero.sql.cmp
import hero.sql.cmpBeforeCursor
import hero.sql.jooq.Tables.COMMUNITY
import hero.sql.jooq.Tables.POST
import hero.sql.jooq.Tables.POST_VOTE
import hero.sql.jooq.Tables.USER
import hero.sql.orderBy
import hero.sql.orderByReversed
import org.jooq.DSLContext
import org.jooq.impl.DSL.jsonbGetAttributeAsText
import java.time.Instant
import java.util.UUID

class CommentQueryService(
    lazyContext: Lazy<DSLContext>,
    private val postRepository: PostRepository,
    private val subscribersCollection: TypedCollectionReference<Subscriber>,
) {
    private val context by lazyContext

    fun execute(query: GetComments): Page<CommentWithMeta> {
        val parentPost = postRepository.findById(query.parentPostId)
            ?.also { validatePostAccess(it, query.userId) }
            ?: throw NotFoundException("Parent ${query.parentPostId} does not exist")
        val rootPost = postRepository.getRootPost(parentPost)
        val rootUserHasPreview = context.select(USER.HAS_POST_PREVIEWS)
            .from(USER)
            .where(USER.ID.eq(rootPost.userId))
            .fetchSingle()
            .value1()
        val rootPostCanBePreviewed = rootPost.hasPreview && rootUserHasPreview

        val afterCursor = query.pageable.afterCursor?.fromBase64()?.fromJson<GetCommentsCursor>()?.lastPublishedAt
        val beforeCursor = query.pageable.beforeCursor?.fromBase64()?.fromJson<GetCommentsCursor>()?.lastPublishedAt
        val userId = query.userId

        val isPostAuthor = query.userId == rootPost.userId
        val communityId = rootPost.communityId?.let { UUID.fromString(it) }
        val subscriptionInfo = if (isPostAuthor) {
            null
        } else if (communityId == null) {
            val subscription = userId?.let { subscribersCollection.fetchActiveSubscription(it, rootPost.userId) }
            if (subscription == null && !rootPostCanBePreviewed) {
                return Page.emptyPage(query.pageable.pageSize)
            } else {
                subscription
            }
        } else {
            null
        }

        val isPartOfCommunity = if (communityId != null) {
            val commentsForMembersOnly = context
                .select(COMMUNITY.TYPE, jsonbGetAttributeAsText(USER.CREATOR, Creator::tierId.name))
                .from(COMMUNITY)
                .join(USER).on(USER.ID.eq(COMMUNITY.OWNER_ID))
                .where(COMMUNITY.ID.eq(communityId))
                .fetchSingle()
                .let { it.value1() to it.value2() }
                .let { (communityType, communityOwnerTier) ->
                    communityType == CONNECTED.name && communityOwnerTier == FREE_SUBSCRIBER_TIER_ID
                }

            val communityIds = userId?.let { context.fetchMemberCommunityIds(it) } ?: emptySet()
            if (communityId !in communityIds && commentsForMembersOnly) {
                return Page.emptyPage(query.pageable.pageSize)
            }
            true
        } else {
            null
        }
        val sort = query.pageable.sort

        val (comments, hasNext) = context
            .select(JooqPostHelper.postFields)
            .select(POST_VOTE.VOTE_VALUE)
            .from(POST)
            .leftJoin(POST_VOTE)
            .on(POST_VOTE.USER_ID.eq(query.userId).and(POST_VOTE.POST_ID.eq(POST.ID)))
            .where(POST.PARENT_ID.eq(query.parentPostId))
            .and(POST.STATE.eq(PostState.PUBLISHED.name))
            .and(POST.TYPE.eq(PostType.COMMENT.name))
            .let {
                if (afterCursor == null && beforeCursor == null) {
                    it
                        .orderBy(POST.PUBLISHED_AT.orderBy(sort))
                } else if (afterCursor != null) {
                    it
                        .and(POST.PUBLISHED_AT.cmp(afterCursor, sort))
                        .orderBy(POST.PUBLISHED_AT.orderBy(sort))
                } else {
                    it
                        .and(POST.PUBLISHED_AT.cmpBeforeCursor(beforeCursor, sort))
                        .orderBy(POST.PUBLISHED_AT.orderByReversed(sort))
                }
            }
            .limit(query.pageable.pageSize + 1)
            .fetch()
            .map { JooqPostHelper.mapRecordToEntity(it) to (it[POST_VOTE.VOTE_VALUE] ?: 0) }
            .map { (comment, myVote) ->
                CommentWithMeta(
                    rootPost,
                    rootUserHasPreview,
                    comment,
                    subscriptionInfo,
                    rootPost.userId,
                    myVote,
                    isPartOfCommunity,
                )
            }
            .let {
                val hasNext = it.size > query.pageable.pageSize
                val comments = if (beforeCursor != null) it.reversed() else it

                comments.take(query.pageable.pageSize) to hasNext
            }

        return Page(comments, nextPageable(comments, query.pageable), hasNext)
    }

    fun execute(query: GetComment): CommentBaseData {
        val comment = postRepository.getById(query.commentId)
        val parentId = comment.parentId ?: throw BadRequestException("${query.commentId} is not a comment")

        val parent = postRepository.getById(parentId)
        val rootPost = postRepository.getRootPost(parent)

        val communityId = rootPost.communityId?.let { UUID.fromString(it) }
        val subscriptionInfo = if (communityId == null) {
            subscribersCollection.fetchActiveSubscription(query.userId, rootPost.userId)
        } else {
            null
        }

        val isPartOfCommunity = if (communityId != null) {
            val communityIds = context.fetchMemberCommunityIds(query.userId)
            communityId in communityIds
        } else {
            null
        }

        val voteValue = context.select(POST_VOTE.VOTE_VALUE)
            .from(POST_VOTE)
            .where(POST_VOTE.USER_ID.eq(query.userId).and(POST_VOTE.POST_ID.eq(query.commentId)))
            .fetchOne()?.value1()
            ?: 0

        return if (parent.parentId == null) {
            CommentData(
                comment = comment,
                rootPost = parent,
                subscriptionInfo = subscriptionInfo,
                myVote = voteValue,
                isPartOfCommunity = isPartOfCommunity,
            )
        } else {
            ReplyData(
                comment = comment,
                parent = parent,
                rootPost = rootPost,
                subscriptionInfo = subscriptionInfo,
                myVote = voteValue,
                isPartOfCommunity = isPartOfCommunity,
            )
        }
    }

    private fun nextPageable(
        comments: List<CommentWithMeta>,
        pageable: Pageable,
    ): Pageable {
        val afterCursor = comments
            .lastOrNull()
            ?.comment
            ?.let {
                val lastPublishedAt = it.published
                GetCommentsCursor(it.id, lastPublishedAt).toJson().toBase64()
            }

        val beforeCursor = comments
            .firstOrNull()
            ?.comment
            ?.let {
                val lastPublishedAt = it.published
                GetCommentsCursor(it.id, lastPublishedAt).toJson().toBase64()
            }

        return PageRequest(-1, pageable.pageSize, afterCursor = afterCursor, beforeCursor = beforeCursor)
    }
}

data class GetComments(val userId: String?, val parentPostId: String, val pageable: Pageable)

data class GetComment(val commentId: String, val userId: String)

sealed class CommentBaseData {
    abstract val comment: Post

    // top level post, post created by the creator
    abstract val rootPost: Post
    abstract val subscriptionInfo: Subscriber?
    abstract val myVote: Int
    abstract val isPartOfCommunity: Boolean?
}

data class ReplyData(
    override val comment: Post,
    override val rootPost: Post,
    override val subscriptionInfo: Subscriber?,
    override val myVote: Int,
    override val isPartOfCommunity: Boolean?,
    val parent: Post,
) : CommentBaseData()

data class CommentData(
    override val comment: Post,
    override val rootPost: Post,
    override val subscriptionInfo: Subscriber?,
    override val myVote: Int,
    override val isPartOfCommunity: Boolean?,
) : CommentBaseData()

data class CommentWithMeta(
    val rootPost: Post,
    val rootPostAuthorHasPreview: Boolean,
    val comment: Post,
    val subscriptionInfo: Subscriber?,
    val postAuthor: String,
    val myVote: Int,
    val isPartOfCommunity: Boolean?,
)

private data class GetCommentsCursor(val lastCommentId: String, val lastPublishedAt: Instant)
