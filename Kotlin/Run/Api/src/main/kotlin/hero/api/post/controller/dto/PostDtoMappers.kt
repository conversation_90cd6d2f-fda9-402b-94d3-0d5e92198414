package hero.api.post.controller.dto

import com.thedeanda.lorem.LoremIpsum
import hero.api.post.service.CommentBaseData
import hero.api.post.service.CommentData
import hero.api.post.service.CommentWithMeta
import hero.api.post.service.CreatorPostWithMeta
import hero.api.post.service.ReplyData
import hero.api.post.service.SavedCreatorPostInfo
import hero.contract.api.dto.CategoryResponse
import hero.contract.api.dto.PostAssetResponse
import hero.contract.api.dto.PostPreviewAssetResponse
import hero.contract.api.dto.PostRelationships
import hero.contract.api.dto.PostResponse
import hero.contract.api.dto.PreviewDocumentResponse
import hero.contract.api.dto.PreviewGjirafaLivestreamResponse
import hero.contract.api.dto.PreviewGjirafaResponse
import hero.contract.api.dto.PreviewGjirafaType
import hero.contract.api.dto.PreviewImageResponse
import hero.contract.api.dto.SavedCreatorPostInfoResponse
import hero.gcloud.imageProxy
import hero.model.Category
import hero.model.DocumentAsset
import hero.model.ImageAsset
import hero.model.ImageAssetDto
import hero.model.Post
import hero.model.PostAsset
import hero.model.Subscriber
import hero.model.SubscriberStatus
import org.jsoup.Jsoup
import org.jsoup.nodes.Element
import org.jsoup.nodes.TextNode
import java.time.Instant

val String.loremize: String
    get() = LoremIpsum(this.hashCode().toLong()).getWords(this.words)

private val String.words: Int
    get() = split("\\s+".toRegex()).size

fun Post.toResponse(
    renderMeta: PostRenderMeta,
    myVote: Int?,
    categories: List<Category> = listOf(),
    savedCreatorPostInfo: SavedCreatorPostInfo? = null,
): PostResponse {
    val hasPreview = renderMeta.previewEnabled && hasPreview && communityId == null
    val textHtmlPreviewDocument = if (hasPreview && !renderMeta.fullResponse) {
        previewHtml(textHtml, renderMeta.previewTextLength)
    } else {
        null
    }

    return PostResponse(
        id = id,
        publishedAt = published,
        pinnedAt = pinnedAt,
        state = state,
        title = when {
            renderMeta.showText -> title?.trim()
            hasPreview -> title?.trim()
            else -> null
        },
        text = when {
            renderMeta.anonymize && renderMeta.showText -> text.loremize
            renderMeta.showText -> text.trim()
            textHtmlPreviewDocument != null -> textHtmlPreviewDocument.text()
            else -> null
        },
        textHtml = when {
            renderMeta.anonymize && renderMeta.showText -> textHtml?.loremize
            renderMeta.showText -> textHtml?.trim()
            textHtmlPreviewDocument != null -> textHtmlPreviewDocument.html()
            else -> null
        },
        textDelta = when {
            renderMeta.anonymize && renderMeta.showText -> textDelta?.loremize
            renderMeta.showText -> textDelta?.trim()
            else -> null
        },
        fullAsset = renderMeta.fullResponse,
        counts = counts,
        excludeFromRss = if (renderMeta.isAuthor) excludeFromRss else null,
        assets = when {
            assets.isNotEmpty() -> assets.filter { !it.isEmpty() }.map { it.toResponse(renderMeta) }
            else -> listOf()
        },
        previewAssets = when {
            renderMeta.fullResponse -> emptyList()
            hasPreview && assets.isNotEmpty() -> {
                assets
                    .filter { !it.isEmpty() }
                    .map { it.toResponse() }
                    .filter { !it.isEmpty() }
            }

            else -> emptyList()
        },
        price = price,
        assetsCount = assets.size,
        categories = categories.map { CategoryResponse(id = it.id, name = it.name, slug = it.slug) },
        savedPostInfo = savedCreatorPostInfo?.let { SavedCreatorPostInfoResponse(it.id, it.savedAt) },
        relationships = PostRelationships(
            userId = userId,
            parentId = parentId,
            siblingId = siblingId,
            messageThreadId = messageThreadId,
        ),
        chapters = chapters ?: emptyList(),
        isAgeRestricted = isAgeRestricted,
        isSponsored = isSponsored,
        pollId = pollId,
        myVote = myVote ?: 0,
        voteScore = voteScore,
        hasPreview = hasPreview,
        hasPreviewInternal = this.hasPreview,
    )
}

fun CreatorPostWithMeta.toResponse(
    requesterId: String?,
    anonymize: Boolean = false,
): PostResponse {
    val isPostAuthor = post.userId == requesterId
    val fullResponse = isPostAuthor ||
        // threads are always full response
        (post.communityId != null) ||
        // user cannot view community post even he subscribes the post author
        (post.communityId == null && isValidSubscription(requesterId, post.userId, subscriptionInfo))

    return post.toResponse(
        PostRenderMeta(
            fullResponse = fullResponse,
            showText = fullResponse,
            isAuthor = isPostAuthor,
            previewEnabled = authorHasPostPreviews,
            anonymize = anonymize,
        ),
        voteValue,
        categories,
        savedPostInfo,
    )
}

fun CommentWithMeta.toResponse(requesterId: String?): PostResponse {
    val isPostAuthor = postAuthor == requesterId
    val showText = rootPost.hasPreview && rootPostAuthorHasPreview
    val fullResponse = isPostAuthor ||
        (rootPost.communityId != null && isPartOfCommunity == true) ||
        // user cannot view community comments even he subscribes the post author
        (rootPost.communityId == null && isValidSubscription(requesterId, postAuthor, subscriptionInfo))

    return comment.toResponse(PostRenderMeta(fullResponse, showText = showText || fullResponse), myVote = myVote)
}

fun CommentBaseData.toResponse(requesterId: String): CommentResponse {
    val isPostAuthor = rootPost.userId == requesterId
    val fullResponse = isPostAuthor ||
        (rootPost.communityId != null && isPartOfCommunity == true) ||
        // user cannot view community comments even he subscribes the post author
        (rootPost.communityId == null && isValidSubscription(requesterId, rootPost.userId, subscriptionInfo))

    return when (this) {
        is CommentData -> {
            val parent = rootPost.toResponse(PostRenderMeta(fullResponse), myVote)
            CommentResponse(
                comment = comment.toResponse(PostRenderMeta(true), myVote),
                parent = parent,
                rootParent = parent,
            )
        }

        is ReplyData -> CommentResponse(
            comment = comment.toResponse(PostRenderMeta(true), myVote),
            parent = parent.toResponse(PostRenderMeta(true), myVote),
            rootParent = rootPost.toResponse(PostRenderMeta(fullResponse), myVote),
        )
    }
}

fun PostAsset.toResponse(renderMeta: PostRenderMeta): PostAssetResponse =
    PostAssetResponse(
        image = image?.toDto(renderMeta),
        youTube = if (renderMeta.fullResponse) youTube else null,
        gjirafa = if (renderMeta.fullResponse) {
            if (gjirafaLive != null) {
                gjirafa?.copy(hasVideo = true)
            } else {
                gjirafa
            }
        } else {
            null
        },
        gjirafaLive = if (renderMeta.fullResponse) gjirafaLive else null,
        document = document?.takeIf {
            renderMeta.fullResponse
        }?.let { DocumentAsset(it.url, it.type, it.name, it.fileSize) },
        thumbnail = thumbnail?.takeIf { renderMeta.fullResponse },
        bunnyAsset = bunnyAsset?.takeIf { renderMeta.fullResponse },
        audioAsset = audioAsset?.takeIf { renderMeta.fullResponse },
        thumbnailImage = thumbnailImage?.toDto(renderMeta),
        timestamp = timestamp,
    )

fun PostAsset.toResponse(): PostPreviewAssetResponse =
    PostPreviewAssetResponse(
        image = image?.let {
            val blurredUrl = it.blurredUrl
            if (blurredUrl != null) {
                PreviewImageResponse(blurredUrl.imageProxy(), it.width, it.height)
            } else {
                null
            }
        },
        document = document?.let {
            PreviewDocumentResponse(
                it.type,
                it.name,
            )
        },
        gjirafa = gjirafa?.let {
            PreviewGjirafaResponse(
                previewStaticUrl = it.previewStaticUrl,
                previewAnimatedUrl = it.previewAnimatedUrl,
                previewStripUrl = it.previewStripUrl,
                duration = it.duration,
                height = it.height,
                width = it.width,
                type = if (it.id.startsWith("v")) PreviewGjirafaType.VIDEO else PreviewGjirafaType.AUDIO,
            )
        },
        gjirafaLive = gjirafaLive?.let {
            PreviewGjirafaLivestreamResponse(this.thumbnailImage())
        },
        thumbnailImage = this.thumbnailImage(),
    )

private fun PostAsset.thumbnailImage() =
    this.thumbnailImage?.toDto(PostRenderMeta(true)) ?: this.thumbnail?.let {
        ImageAssetDto(it.imageProxy(), 0, 0, null, null)
    }

private fun PostPreviewAssetResponse.isEmpty(): Boolean =
    image == null && gjirafa == null && document == null && gjirafaLive == null

private fun ImageAsset.toDto(renderMeta: PostRenderMeta) =
    if (renderMeta.fullResponse) {
        ImageAssetDto(
            url = id.imageProxy(),
            width = width,
            height = height,
            fileName = fileName,
            fileSize = fileSize,
        )
    } else {
        null
    }

private fun isValidSubscription(
    requesterId: String?,
    creatorId: String,
    sub: Subscriber?,
): Boolean {
    if (sub == null || requesterId == null) {
        return false
    }
    if (requesterId != sub.userId) {
        error("Requester id [$requesterId] is different than fetched subscriber user id [${sub.userId}]")
    }
    if (creatorId != sub.creatorId) {
        error("Post creator id [$creatorId] is different than fetched subscriber creator id [${sub.creatorId}]")
    }

    val expires = sub.expires
    val isNotExpired = expires == null || expires.isAfter(Instant.now())
    return isNotExpired && sub.status in SubscriberStatus.activeStatuses
}

data class PostRenderMeta(
    val fullResponse: Boolean,
    val paymentsFromUserIds: List<String>? = null,
    val showText: Boolean = fullResponse,
    val isAuthor: Boolean = false,
    val previewEnabled: Boolean = false,
    val previewTextLength: Int = 250,
    val anonymize: Boolean = false,
)

private fun previewHtml(
    html: String?,
    length: Int,
): Element? {
    if (html == null || html.isBlank() == true) {
        return null
    }

    val parsedHtml = Jsoup.parse(html)
    var remainingLength = length
    parsedHtml.traverse { node, depth ->
        if (node is TextNode && node.text().isNotBlank()) {
            val str = node.text().take(remainingLength)
            node.text(str)
            remainingLength -= str.length
        }

        if (node is TextNode && node.text().isBlank()) {
            node.text("")
        }
    }

    return parsedHtml
        .apply {
            select("a").unwrap()
        }
        .body()
}
