package hero.api.notification.service

import hero.baseutils.fromBase64
import hero.baseutils.toBase64
import hero.core.data.Page
import hero.core.data.PageRequest
import hero.core.data.Pageable
import hero.jackson.fromJson
import hero.jackson.toJson
import hero.model.Notification
import hero.model.NotificationType
import hero.model.User
import hero.repository.notification.NotificationRepository
import hero.repository.user.UserRepository
import hero.sql.jooq.Tables
import hero.sql.jooq.Tables.NOTIFICATION
import org.jooq.Record
import org.jooq.SelectConditionStep
import java.time.Instant
import java.util.EnumSet

class NotificationQueryService(
    private val notificationRepository: NotificationRepository,
    private val userRepository: UserRepository,
) {
    fun execute(query: GetNotifications): Page<NotificationWithData> {
        val afterCursor = query.pageable.afterCursor?.fromBase64()?.fromJson<GetNotificationsAfterCursor>()
        val beforeCursor = query.pageable.beforeCursor?.fromBase64()?.fromJson<GetNotificationsBeforeCursor>()
        val types = query.categories.flatMap { it.types }.map { it.name }

        val (notifications, hasNext) = notificationRepository
            .find {
                this
                    .where(NOTIFICATION.USER_ID.eq(query.userId))
                    .and(NOTIFICATION.DELETED_AT.isNull)
                    .let {
                        if (types.isNotEmpty() && NotificationTypeCategory.COMMUNITY !in query.categories)
                            it.and(NOTIFICATION.TYPE.`in`(types))
                        else if (types.isNotEmpty() && NotificationTypeCategory.COMMUNITY in query.categories)
                            it.and(NOTIFICATION.TYPE.`in`(types).or(NOTIFICATION.COMMUNITY_ID.isNotNull))
                        else
                            it
                    }
                    .editByCursor(afterCursor, beforeCursor)
                    .orderBy(NOTIFICATION.CREATED_AT.desc())
                    .limit(query.pageable.pageSize + 1)
            }
            .let {
                it.take(query.pageable.pageSize) to (it.size > query.pageable.pageSize)
            }

        val userIds = notifications.map { it.actorIds.last() }.toSet()
        val lastActorsById = userRepository
            .find { where(Tables.USER.ID.`in`(userIds)) }
            .associateBy { it.id }

        val notificationsWithData = notifications.map {
            NotificationWithData(it, lastActorsById[it.actorIds.last()])
        }

        return Page(notificationsWithData, nextPageable(notifications, query.pageable), hasNext)
    }

    private fun <T : Record> SelectConditionStep<T>.editByCursor(
        afterCursor: GetNotificationsAfterCursor?,
        beforeCursor: GetNotificationsBeforeCursor?,
    ) = when {
        // TODO remove cursors by id in the future
        afterCursor?.createdAt != null -> this.and(NOTIFICATION.CREATED_AT.lt(afterCursor.createdAt))
        afterCursor != null -> this.and(NOTIFICATION.ID.lt(afterCursor.lastId))
        beforeCursor?.createdAt != null -> this.and(NOTIFICATION.CREATED_AT.gt(beforeCursor.createdAt))
        beforeCursor != null -> this.and(NOTIFICATION.ID.gt(beforeCursor.firstId))
        else -> this
    }

    private fun nextPageable(
        notifications: List<Notification>,
        pageable: Pageable,
    ): PageRequest {
        val afterCursor = notifications
            .lastOrNull()
            ?.let {
                GetNotificationsAfterCursor(it.id, it.created).toJson().toBase64()
            }

        val beforeCursor = notifications
            .firstOrNull()
            ?.let {
                GetNotificationsBeforeCursor(it.id, it.created).toJson().toBase64()
            }

        return PageRequest(-1, pageable.pageSize, afterCursor = afterCursor, beforeCursor = beforeCursor)
    }
}

data class GetNotifications(
    val userId: String,
    val pageable: Pageable,
    val categories: Set<NotificationTypeCategory> = emptySet(),
)

// TODO also add post and message when we merge User and Post service
data class NotificationWithData(
    val notification: Notification,
    val lastActor: User?,
)

private data class GetNotificationsAfterCursor(val lastId: String, val createdAt: Instant?)

private data class GetNotificationsBeforeCursor(val firstId: String, val createdAt: Instant?)

enum class NotificationTypeCategory(val types: EnumSet<NotificationType>) {
    POST(
        EnumSet.of(
            NotificationType.PAID_POST,
            NotificationType.NEW_POST,
            NotificationType.NEW_LIVESTREAM,
        ),
    ),
    COMMENT(
        EnumSet.of(
            NotificationType.NEW_COMMENT,
            NotificationType.NEW_REPLY,
            NotificationType.NEW_REPLY_TO_REPLY,
        ),
    ),
    SUBSCRIPTION(
        EnumSet.of(
            NotificationType.NEW_SUBSCRIPTION,
            NotificationType.CANCELLED_SUBSCRIPTION_ENDED,
            NotificationType.CANCELLED_SUBSCRIPTION_INSUFFICIENT_FUNDS,
            NotificationType.CANCELLED_SUBSCRIPTION_REFUSED,
            NotificationType.CANCELLED_SUBSCRIPTION_REFUNDED,
            NotificationType.CANCELLED_SUBSCRIPTION_BY_CREATOR,
            NotificationType.CANCELLED_SUBSCRIPTION_OTHER,
        ),
    ),
    REQUEST(
        EnumSet.of(NotificationType.SUBSCRIBE_REQUEST_ACCEPTED),
    ),
    COMMUNITY(
        EnumSet.of(NotificationType.NEW_THREAD),
    ),
}
