package hero.api.community.service

import com.fasterxml.jackson.annotation.JsonTypeInfo
import hero.baseutils.fromBase64
import hero.baseutils.toBase64
import hero.core.data.Page
import hero.core.data.PageRequest
import hero.core.data.Pageable
import hero.core.data.Sort
import hero.exceptions.http.BadRequestException
import hero.exceptions.http.NotFoundException
import hero.jackson.fromJson
import hero.jackson.toJson
import hero.model.Community
import hero.model.CommunityMemberStatus.ACTIVE
import hero.model.User
import hero.repository.community.JooqCommunityHelper
import hero.repository.user.JooqUserHelper
import hero.sql.jooq.Tables.COMMUNITY
import hero.sql.jooq.Tables.COMMUNITY_MEMBER
import hero.sql.jooq.Tables.NOTIFICATION_SETTINGS
import hero.sql.jooq.Tables.USER
import hero.sql.orderBy
import hero.sql.tupleSorting
import hero.sql.wordSimilarity
import org.jooq.DSLContext
import org.jooq.Record
import org.jooq.exception.NoDataFoundException
import org.jooq.impl.DSL
import java.time.Instant
import java.util.UUID

class CommunityQueryService(
    lazyContext: Lazy<DSLContext>,
) {
    private val context by lazyContext

    fun execute(query: GetCommunity): CommunityWithMeta {
        val communityId = query.communityId
        val slug = query.slug
        if (communityId == null && slug == null) {
            throw BadRequestException("Either communityId or slug must be provided")
        }

        if (communityId != null && slug != null) {
            throw BadRequestException("Only one of communityId or slug must be provided")
        }

        val (community, owner, isCommunityMember) = context
            .select(JooqCommunityHelper.communityFields)
            .select(COMMUNITY_MEMBER.STATE)
            .select(JooqUserHelper.userFields)
            .from(COMMUNITY)
            .join(USER).on(USER.ID.eq(COMMUNITY.OWNER_ID))
            .join(NOTIFICATION_SETTINGS).on(NOTIFICATION_SETTINGS.USER_ID.eq(USER.ID))
            .leftJoin(COMMUNITY_MEMBER)
            .on(COMMUNITY_MEMBER.COMMUNITY_ID.eq(COMMUNITY.ID)).and(COMMUNITY_MEMBER.USER_ID.eq(query.userId))
            .let {
                if (slug != null) {
                    it.where(COMMUNITY.SLUG.eq(slug))
                } else {
                    it.where(COMMUNITY.ID.eq(communityId))
                }
            }
            .runCatching {
                val result = fetchSingle()
                Triple(
                    JooqCommunityHelper.mapRecordToEntity(result),
                    JooqUserHelper.mapRecordToEntity(result),
                    result.isMember(query.userId),
                )
            }
            .getOrElse {
                when (it) {
                    is NoDataFoundException -> throw NotFoundException()
                    else -> throw it
                }
            }

        return CommunityWithMeta(community, isCommunityMember, owner)
    }

    fun execute(query: GetCommunities): Page<CommunityWithMeta> {
        val filter = query.filter
        val afterCursor = query.pageable.afterCursor?.fromBase64()?.fromJson<GetCommunitiesCursor>()

        val sort = query.pageable.sort
        val sortBy = sort.by?.let { CommunitiesSortingFields.valueOf(it) } ?: CommunitiesSortingFields.CREATED_AT

        val (similarityField, similarityNameCondition) = filter.query?.let {
            similarityField(it) to wordSimilarity(it, COMMUNITY.NAME)
        } ?: Pair(null, null)

        val (communities, hasNext) = context
            .select(JooqCommunityHelper.communityFields)
            .select(COMMUNITY_MEMBER.STATE)
            .select(JooqUserHelper.userFields)
            .let { similarityField?.let { simField -> it.select(simField) } ?: it }
            .from(COMMUNITY)
            .join(USER).on(USER.ID.eq(COMMUNITY.OWNER_ID))
            .join(NOTIFICATION_SETTINGS).on(NOTIFICATION_SETTINGS.USER_ID.eq(USER.ID))
            .leftJoin(COMMUNITY_MEMBER)
            .on(COMMUNITY_MEMBER.COMMUNITY_ID.eq(COMMUNITY.ID)).and(COMMUNITY_MEMBER.USER_ID.eq(query.userId))
            .where(DSL.trueCondition())
            .let { filter.ownerId?.let { ownerId -> it.and(COMMUNITY.OWNER_ID.eq(query.filter.ownerId)) } ?: it }
            .let { filter.isMember?.let { isMember -> it.and(COMMUNITY_MEMBER.STATE.eq(ACTIVE.name)) } ?: it }
            .let { similarityNameCondition?.let { snc -> it.and(snc) } ?: it }
            .let {
                if (afterCursor != null) {
                    when (afterCursor) {
                        is GetCommunitiesCreatedAtCursor -> it.tupleSorting(
                            COMMUNITY.CREATED_AT,
                            COMMUNITY.ID,
                            afterCursor.createdAt,
                            afterCursor.id,
                            Sort(direction = sort.direction),
                        )

                        is GetCommunitiesThreadsCountCursor -> it.tupleSorting(
                            COMMUNITY.THREADS_COUNT,
                            COMMUNITY.ID,
                            afterCursor.threadsCount,
                            afterCursor.id,
                            Sort(direction = sort.direction),
                        )
                    }
                } else {
                    when (sortBy) {
                        CommunitiesSortingFields.THREADS_COUNT -> it.orderBy(
                            COMMUNITY.THREADS_COUNT.orderBy(sort),
                            COMMUNITY.ID.orderBy(sort),
                        )

                        CommunitiesSortingFields.QUERY_SIMILARITY -> it.orderBy(
                            similarityField?.orderBy(sort),
                            COMMUNITY.ID.orderBy(sort),
                        )

                        CommunitiesSortingFields.CREATED_AT -> it.orderBy(
                            COMMUNITY.CREATED_AT.orderBy(sort),
                            COMMUNITY.ID.orderBy(sort),
                        )
                    }
                }
            }
            .limit(query.pageable.pageSize + 1)
            .fetch()
            .map {
                val community = JooqCommunityHelper.mapRecordToEntity(it)
                val isMember = it.isMember(query.userId)
                val owner = JooqUserHelper.mapRecordToEntity(it)
                CommunityWithMeta(community, isMember, owner)
            }
            .let {
                it.take(query.pageable.pageSize) to (it.size > query.pageable.pageSize)
            }

        return Page(communities, hasNext = hasNext, nextPageable = nextPageable(communities, sortBy, query.pageable))
    }

    private fun similarityField(query: String) =
        DSL.field(
            "word_similarity(immutable_unaccent({0}), immutable_unaccent({1}))",
            Double::class.java,
            query,
            COMMUNITY.NAME,
        )

    private fun nextPageable(
        communities: List<CommunityWithMeta>,
        sortBy: CommunitiesSortingFields,
        pageable: Pageable,
    ): Pageable {
        val afterCursor = communities
            .lastOrNull()
            ?.let {
                when (sortBy) {
                    // we don't allow paging by queries atm
                    CommunitiesSortingFields.QUERY_SIMILARITY -> null

                    CommunitiesSortingFields.THREADS_COUNT -> GetCommunitiesThreadsCountCursor(
                        it.community.threadsCount,
                        it.community.id,
                    )

                    CommunitiesSortingFields.CREATED_AT -> GetCommunitiesCreatedAtCursor(
                        it.community.createdAt,
                        it.community.id,
                    )
                }
            }
            ?.toJson()
            ?.toBase64()

        return PageRequest(
            pageSize = pageable.pageSize,
            sort = pageable.sort,
            afterCursor = afterCursor,
        )
    }

    private fun Record.isMember(userId: String?): Boolean =
        userId == this[COMMUNITY.OWNER_ID] || this[COMMUNITY_MEMBER.STATE] == ACTIVE.name
}

data class GetCommunity(val communityId: UUID?, val slug: String?, val userId: String?)

data class GetCommunities(
    val userId: String?,
    val filter: GetCommunitiesFilter,
    val pageable: Pageable = PageRequest(),
)

data class GetCommunitiesFilter(
    val ownerId: String? = null,
    val isMember: Boolean? = null,
    val query: String? = null,
)

data class CommunityWithMeta(val community: Community, val isMember: Boolean, val owner: User)

@JsonTypeInfo(use = JsonTypeInfo.Id.NAME)
private sealed class GetCommunitiesCursor

private data class GetCommunitiesCreatedAtCursor(val createdAt: Instant, val id: UUID) : GetCommunitiesCursor()

private data class GetCommunitiesThreadsCountCursor(val threadsCount: Long, val id: UUID) : GetCommunitiesCursor()

enum class CommunitiesSortingFields {
    THREADS_COUNT,
    QUERY_SIMILARITY,
    CREATED_AT,
}
