package hero.functions

import com.stripe.exception.InvalidRequestException
import com.stripe.model.Transfer
import com.stripe.net.RequestOptions
import com.stripe.param.TransferCreateParams
import com.stripe.param.TransferListParams
import hero.baseutils.SystemEnv
import hero.baseutils.log
import hero.gcloud.FirestoreRef
import hero.gcloud.TypedCollectionReference
import hero.gcloud.firestore
import hero.gcloud.get
import hero.gcloud.isNull
import hero.gcloud.typedCollectionOf
import hero.gcloud.where
import hero.model.AppleCharge
import hero.model.Currency
import hero.model.Subscriber
import hero.model.Tier
import hero.model.topics.Hourly
import hero.stripe.model.StripeKeys
import hero.stripe.service.StripeClients
import java.time.Instant

@Suppress("unused")
class StripeAppleTransferrer(
    production: Boolean = SystemEnv.isProduction,
    firestore: FirestoreRef = firestore(SystemEnv.cloudProject, production),
) : PubSubSubscriber<Hourly>() {
    private val stripeKeysRepository = TypedCollectionReference<StripeKeys>(firestore.firestore["constants"])
    private val stripeClients = StripeClients(SystemEnv.stripeKeyEu, SystemEnv.stripeKeyUs)
    private val appleCharges = firestore.typedCollectionOf(AppleCharge)

    override fun consume(payload: Hourly) {
        fetchPendingTransfers()
            .forEach { appleCharge ->
                val tier = Tier.ofId(appleCharge.tierId)
                val logContext = createLogContext(appleCharge)

                // validate the transfer does not exist yet and be idempotent
                fetchStripeTransfer(tier.currency, appleCharge.appleReferenceId, appleCharge.appleTransactionId)
                    ?.let {
                        log.info(
                            "Transfer for ${appleCharge.appleTransactionId} already exists, skipping",
                            logContext,
                        )
                        updateStripeTransfer(appleCharge.appleTransactionId, it.id)
                        return@forEach
                    }

                val stripeTransfer = try {
                    createStripeTransfer(
                        tier.currency,
                        appleCharge.appleTransactionId,
                        buildTransferParams(appleCharge, tier),
                    )
                } catch (e: InvalidRequestException) {
                    if (e.code == "balance_insufficient") {
                        val balance = try {
                            stripeClients[tier.currency].balance().retrieve()
                        } catch (e: Exception) {
                            log.error("Cannot fetch current balance: ${e.message}", cause = e)
                            null
                        }
                        log.error(
                            "Insufficient balance to perform Apple->Stripe transfer" +
                                " ${tier.currency} ${appleCharge.transferCents}" +
                                " (will be retried), available was ${balance?.available}",
                            logContext,
                        )
                        // at this moment we are fully done, need to wait for balance to top up
                        return
                    }
                    log.fatal("Cannot create transfer for ${appleCharge.appleTransactionId}: ${e.message}", logContext)
                    return@forEach
                }

                log.info(
                    "Transferring ${stripeTransfer.currency} ${stripeTransfer.amount}" +
                        " of ${appleCharge.appleTransactionId} to ${appleCharge.targetAccountId}",
                    logContext,
                )

                updateStripeTransfer(appleCharge.appleTransactionId, stripeTransfer.id)
            }
    }

    private fun buildTransferParams(
        appleCharge: AppleCharge,
        tier: Tier,
    ): TransferCreateParams =
        TransferCreateParams.builder()
            .setAmount(appleCharge.transferCents)
            .setCurrency(tier.currency.name.lowercase())
            .putMetadata(Subscriber::applePriceCents.name, appleCharge.priceStoreCents.toString())
            .putMetadata(Subscriber::appleCurrency.name, appleCharge.currencyStore.name)
            .putMetadata(AppleCharge::appleReferenceId.name, appleCharge.appleReferenceId)
            .putMetadata(AppleCharge::appleTransactionId.name, appleCharge.appleTransactionId)
            .putMetadata(AppleCharge::userId.name, appleCharge.userId)
            .putMetadata(AppleCharge::creatorId.name, appleCharge.creatorId)
            .putMetadata(AppleCharge::tierId.name, appleCharge.tierId)
            .putMetadata(AppleCharge::customerId.name, appleCharge.customerId!!)
            .putMetadata(AppleCharge::createdAt.name, appleCharge.createdAt.toString())
            .setDestination(appleCharge.targetAccountId)
            .setTransferGroup(appleCharge.appleReferenceId)
            .build()

    private fun createLogContext(transfer: AppleCharge): Map<String, String> =
        mapOf(
            "userId" to transfer.userId,
            "creatorId" to transfer.creatorId,
            "appleTransactionId" to transfer.appleTransactionId,
            "appleReferenceId" to transfer.appleReferenceId,
        )

    internal fun updateStripeTransfer(
        appleTransactionId: String,
        stripeTransferId: String,
    ) {
        val entity = appleCharges[appleTransactionId]
        entity.field(AppleCharge::stripeTransferId).update(stripeTransferId)
        entity.field(AppleCharge::transferredAt).update(Instant.now())
    }

    internal fun createStripeTransfer(
        currency: Currency,
        appleTransactionId: String,
        transferParams: TransferCreateParams,
    ): Transfer =
        stripeClients[currency].transfers()
            .create(transferParams, RequestOptions.builder().setIdempotencyKey(appleTransactionId).build())

    internal fun fetchStripeTransfer(
        currency: Currency,
        appleReferenceId: String,
        appleTransactionId: String,
    ): Transfer? =
        stripeClients[currency].transfers()
            .list(TransferListParams.builder().setTransferGroup(appleReferenceId).build())
            .autoPagingIterable()
            .filter { !it.reversed }
            .firstOrNull { it.metadata[Subscriber::appleTransactionId.name] == appleTransactionId }

    internal fun fetchPendingTransfers(): List<AppleCharge> =
        appleCharges
            .where(AppleCharge::stripeTransferId).isNull()
            .fetchAll()
}
