package hero.functions

import hero.baseutils.SystemEnv
import hero.baseutils.log
import hero.gcloud.FirestoreRef
import hero.gcloud.PubSub
import hero.gcloud.TypedCollectionReference
import hero.gcloud.firestore
import hero.gcloud.typedCollectionOf
import hero.model.CZ_VAT_COUNTRY
import hero.model.Currency
import hero.model.FREE_SUBSCRIBER_TIER_ID
import hero.model.Tier
import hero.model.User
import hero.model.UserStateChange
import hero.model.UserStateChanged
import hero.sql.ConnectorConnectionPool
import hero.sql.jooq.JooqSQL
import hero.sql.jooq.Tables.SUBSCRIPTION
import hero.stripe.model.StripePrice
import hero.stripe.service.StripeClients
import hero.stripe.service.StripeCouponService
import hero.stripe.service.StripePaymentMethodsService
import hero.stripe.service.StripeService
import hero.stripe.service.StripeSubscriptionService
import hero.stripe.service.VatMapping
import org.jooq.DSLContext
import java.time.Instant

@Suppress("Unused")
class VerifiedUserHandler(
    lazyContext: Lazy<DSLContext> = lazy { JooqSQL.context(ConnectorConnectionPool.dataSource) },
    pubSub: PubSub = PubSub(SystemEnv.environment, SystemEnv.cloudProject),
    firestore: FirestoreRef = firestore(SystemEnv.cloudProject, SystemEnv.isProduction),
    private val usersCollection: TypedCollectionReference<User> = firestore.typedCollectionOf(User),
    private val priceCollection: TypedCollectionReference<StripePrice> = firestore.typedCollectionOf(StripePrice),
    private val heroheroCreator: User = usersCollection["infoheroherokamniiih"].get(),
    private val heroheroPrice: StripePrice = priceCollection["${heroheroCreator.id}|$FREE_SUBSCRIBER_TIER_ID"].get(),
) : PubSubSubscriber<UserStateChanged>() {
    private val context by lazyContext
    private val heroheroFreeTier = Tier.ofId(FREE_SUBSCRIBER_TIER_ID)
    private val stripeClients = StripeClients(SystemEnv.stripeKeyEu, SystemEnv.stripeKeyUs)
    private val stripeCouponService = StripeCouponService(stripeClients)
    private val stripeService = StripeService(stripeClients, pubSub)
    private val paymentMethodsService = StripePaymentMethodsService(stripeClients, stripeService, pubSub)

    private val stripeSubscriptionService = StripeSubscriptionService(
        clients = stripeClients,
        stripeCouponService = stripeCouponService,
        paymentMethodsService = paymentMethodsService,
        production = SystemEnv.isProduction,
        countryToVatMapping = VatMapping(emptyMap()),
    )

    override fun consume(payload: UserStateChanged) {
        val user = payload.user
        if (payload.stateChange != UserStateChange.PATCHED) {
            return
        }

        if (!user.creator.verified) {
            return
        }

        if (user.image?.id == null) {
            return
        }

        if (user.company?.country?.uppercase() !in setOf("CZ", "SK")) {
            return
        }

        if (hasSubscriptionToHerohero(user.id)) {
            return
        }

        val customerId = customerFactory(payload.user.id, heroheroFreeTier.currency)
        subscribe(user.id, customerId)
    }

    internal fun hasSubscriptionToHerohero(userId: String): Boolean =
        context.selectFrom(SUBSCRIPTION)
            // we must consider all subscriptions, not only active,
            // to avoid re-adding unsubscribed users
            .where(
                SUBSCRIPTION.USER_ID.eq(userId)
                    .and(SUBSCRIPTION.CREATOR_ID.eq(heroheroCreator.id)),
            )
            .fetch()
            .isNotEmpty

    // WARN duplicated from SubscriberStripeRepository
    internal fun customerFactory(
        userId: String,
        currency: Currency,
    ): String {
        val userReference = usersCollection[userId]
        val user = userReference.get()
        val customerId = user.customerIds[currency.name]
        return if (customerId == null) {
            val customer = stripeService.createCustomer(user, currency)
            user.customerIds[currency.name] = customer.id
            userReference.field(User::customerIds).update(user.customerIds)
            log.info(
                "Created a new Stripe $currency customer ${customer.id} for ${user.id}",
                mapOf("userId" to user.id),
            )
            customer.id!!
        } else {
            customerId
        }
    }

    internal fun subscribe(
        userId: String,
        customerId: String,
    ) {
        stripeSubscriptionService.createSubscription(
            userId = userId,
            customerId = customerId,
            paymentMethodId = null,
            couponId = null,
            tier = heroheroFreeTier,
            priceId = heroheroPrice.stripeId,
            creatorId = heroheroCreator.id,
            creatorStripeAccountId = null,
            onBehalfOf = null,
            creatorCountry = CZ_VAT_COUNTRY,
            creatorVatId = null,
            subscribed = Instant.now(),
            isResubscription = false,
            cardCreateType = null,
            currency = heroheroFreeTier.currency,
        )
    }
}
