package hero.repository.device

import hero.sql.jooq.Tables.DEVICE
import org.jooq.DSLContext

fun fetchFirebaseRegistrationTokens(
    context: DSLContext,
    userId: String,
): List<String> =
    context
        .selectDistinct(DEVICE.FIREBASE_REGISTRATION_TOKEN)
        .from(DEVICE)
        .where(DEVICE.USER_ID.eq(userId))
        .and(DEVICE.DISABLED_AT.isNull)
        .fetch()
        .map { it[DEVICE.FIREBASE_REGISTRATION_TOKEN] }

fun DSLContext.fetchPushTokens(userId: String): List<String> = fetchFirebaseRegistrationTokens(this, userId)
