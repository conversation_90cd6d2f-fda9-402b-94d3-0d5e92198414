package hero.model

import hero.baseutils.log
import hero.core.annotation.NoArg
import hero.core.data.EntityCollection
import hero.model.GjirafaStatus.COMPLETE
import hero.model.GjirafaStatus.PARTIALLY_COMPLETED
import hero.model.topics.PostState
import java.time.Instant

@NoArg
data class Post(
    val id: String,
    var parentId: String? = null,
    var siblingId: String? = null,
    var userId: String,
    /** User id of root parent post */
    var parentUserId: String? = null,
    /** Id of root parent post, very unfortunate naming from me */
    val parentPostId: String? = null,
    var messageThreadId: String? = null,
    /** Is a uuid, but cannot be deserialized and serialized using firestore */
    val communityId: String? = null,
    var updated: Instant = Instant.now(),
    val created: Instant = Instant.now(),
    var published: Instant,
    val deletedAt: Instant? = null,
    var pinnedAt: Instant? = null,
    var notifiedAt: Instant? = null,
    var state: PostState,
    var title: String? = null,
    var text: String,
    var textHtml: String? = null,
    @Deprecated("Remove when new HTML editor is implemented")
    var textDelta: String? = null,
    // unfortunately we cannot query directly `assets[].mux.id`,
    // we need to duplicate ids to assetIds plain array to be queried
    // https://stackoverflow.com/a/54081889/922584
    var assetIds: List<String> = emptyList(),
    var assetStates: List<GjirafaStatus> = emptyList(),
    var assets: List<PostAsset> = emptyList(),
    var counts: PostCounts = PostCounts(),
    var participingUserIds: List<String> = emptyList(),
    var price: Long? = null,
    var excludeFromRss: Boolean = false,
    var categories: List<String> = emptyList(),
    var chapters: List<Chapter>? = emptyList(),
    var isAgeRestricted: Boolean = false,
    var isSponsored: Boolean = false,
    var hasPreview: Boolean = true,
    var poll: Poll? = null,
    var pollId: String? = null,
    val views: Long = 0,
    val voteScore: Int = 0,
) {
    fun isFreeMessage(): Boolean = price != null && price == 0L

    fun isLivestream(): Boolean = assets.any { it.gjirafa == null && it.gjirafaLive != null }

    fun isLivestreamLive(): Boolean =
        assets.any { it.gjirafa == null && it.gjirafaLive?.liveStatus == LiveVideoStatus.LIVE }

    private val assetsReady: Boolean
        get() = assets.mapNotNull { it.gjirafa }.all { it.status in setOf(COMPLETE, PARTIALLY_COMPLETED) }

    companion object : EntityCollection<Post> {
        override val collectionName: String = "posts"
    }

    // TODO shift away from the entity
    fun refreshAssets(refreshedAsset: GjirafaAsset) {
        log.info(
            "Refreshing asset ${refreshedAsset.id} in post $id.",
            mapOf("assetId" to refreshedAsset.id, "postId" to id, "originalState" to state),
        )

        assets = assets.map {
            // this will also append regular asset to a livestream, once it is `stopped-and-cut`
            if (it.gjirafa?.id == refreshedAsset.id || it.gjirafaLive?.id == refreshedAsset.id) {
                it.copy(gjirafa = refreshedAsset)
            } else {
                it
            }
        }

        if (state == PostState.PROCESSING) {
            if (assetsReady) {
                state = if (published.isAfter(Instant.now())) {
                    PostState.SCHEDULED
                } else {
                    PostState.PUBLISHED
                }
                log.info(
                    "Post state is now $state as all assets are now READY.",
                    mapOf("userId" to userId, "postId" to id),
                )
            } else {
                log.info(
                    "Not all assets are yet ready, post still in PROCESSING state.",
                    mapOf("userId" to userId, "postId" to id),
                )
            }
        }
    }
}

typealias PollOptionId = String

@NoArg
data class Poll(
    val id: String,
    val deadline: Instant,
    val options: Map<PollOptionId, PollOption>,
)

@NoArg
data class PollOption(
    val id: PollOptionId,
    val title: String,
    val voteCount: Long,
    val index: Int,
)

@NoArg
data class AssetAnnotation(
    val adult: AssetAnnotationResult,
    val spoof: AssetAnnotationResult,
    val medical: AssetAnnotationResult,
    val violence: AssetAnnotationResult,
    val racy: AssetAnnotationResult,
)

@NoArg
data class AssetAnnotationResult(
    val value: Int,
    val textValue: String,
)

@NoArg
data class PostPayment(
    val userId: String,
    val postId: String,
    val timestamp: Instant,
    val id: String = id(userId = userId, postId = postId),
) {
    companion object : EntityCollection<PostPayment> {
        override val collectionName: String = "post-payments"

        fun id(
            userId: String,
            postId: String,
        ) = "pp-$postId-$userId"
    }
}

data class PostCounts(
    var comments: Long = 0,
    var replies: Long = 0,
)

data class PostAsset(
    var image: ImageAsset? = null,
    var youTube: YouTubeAsset? = null,
    var gjirafa: GjirafaAsset? = null,
    var gjirafaLive: GjirafaLiveAsset? = null,
    var assetAnnotations: AssetAnnotation? = null,
    var bunnyAsset: String? = null,
    var audioAsset: String? = null,
    var document: DocumentAsset? = null,
    @Deprecated("Use thumbnailImage instead.")
    var thumbnail: String? = null,
    var thumbnailBlurUrl: String? = null,
    var thumbnailImage: ImageAsset? = null,
    val analysis: PostAssetAnalysis? = null,
    // this value is never written, only read from the database
    // so do not bother setting this value
    val timestamp: Double? = null,
) {
    fun isEmpty(): Boolean =
        (image == null || image!!.hidden) &&
            (gjirafa == null || gjirafa!!.hidden) &&
            youTube == null &&
            bunnyAsset == null &&
            audioAsset == null &&
            document == null &&
            gjirafaLive == null

    // nullable since asset can have no field set, maybe throw error here instead
    fun assetType(): PostAssetType? =
        if (image != null)
            PostAssetType.IMAGE
        else if (youTube != null)
            PostAssetType.YOUTUBE
        else if (gjirafa != null)
            PostAssetType.GJIRAFA
        else if (gjirafaLive != null)
            PostAssetType.GJIRAFA_LIVESTREAM
        else if (document != null)
            PostAssetType.DOCUMENT
        else if (bunnyAsset != null)
            PostAssetType.BUNNY
        else
            null
}

data class PostAssetAnalysis(
    val summary: String,
    val toxicityScore: PostAssetAnalysisScore,
)

data class PostAssetAnalysisScore(
    val score: Int,
    val reason: String,
)

enum class PostAssetType {
    IMAGE,
    YOUTUBE,
    GJIRAFA,
    GJIRAFA_LIVESTREAM,
    DOCUMENT,
    BUNNY,
}

data class PostDto(
    override val id: String?,
    override val attributes: PostDtoAttributes,
    override val relationships: PostDtoRelationships,
) : JsonApiEntity {
    override val type: String = "post"
}

data class PostDtoAttributes(
    var state: PostState? = null,
    var title: String? = null,
    var text: String? = null,
    var textHtml: String? = null,
    var textDelta: String? = null,
    var fullAsset: Boolean? = null,
    var excludeFromRss: Boolean? = null,
    var pinnedAt: Instant? = null,
    val assets: List<PostAssetDto> = emptyList(),
    var counts: PostCounts? = null,
    var publishedAt: Instant? = null,
    var price: Long? = null,
    var assetsCount: Int? = null,
    var chapters: List<Chapter> = emptyList(),
    var isAgeRestricted: Boolean = false,
    var isSponsored: Boolean = false,
    var hasPreview: Boolean = true,
    var poll: PollDto? = null,
)

data class PollDto(
    val id: String?,
    val options: List<PollOptionDto>,
    val deadline: Instant?,
)

data class PollOptionDto(
    val id: String?,
    val title: String,
)

@NoArg
data class Chapter(
    val title: String,
    val time: Int,
)

data class PostDtoRelationships(
    var parent: PostDtoRelationship? = null,
    var sibling: PostDtoRelationship? = null,
    var user: UserDtoRelationship? = null,
    var messageThread: MessageThreadDtoV2Relationship? = null,
    var paymentsByUsers: List<UserDtoRelationship> = emptyList(),
    var categories: List<CategoryDtoRelationship> = emptyList(),
)

data class PostDtoRelationship(
    val id: String,
) {
    val type: String = "post"
}

@NoArg
data class PostAssetDto(
    val image: ImageAssetDto? = null,
    val youTube: YouTubeAsset? = null,
    val gjirafa: GjirafaAsset? = null,
    val gjirafaLive: GjirafaLiveAsset? = null,
    val document: DocumentAsset? = null,
    val thumbnail: String? = null,
    val bunnyAsset: String? = null,
    val audioAsset: String? = null,
    val thumbnailImage: ImageAssetDto? = null,
)

@NoArg
data class DocumentAsset(
    val url: String,
    val type: DocumentType,
    // TODO rename to fileName
    val name: String?,
    val fileSize: Long?,
)

enum class DocumentType {
    // documents
    DOCX,
    PPTX,
    XLSX,
    PDF,
    EPUB,
    // gps tracks
    GPX,
    // audio files
    GP5,
    MIDI,
    // raw photos
    RAW,
    ARW,
    /** 3D files */
    OBJ,
    FBX,
    GLTF,
    GLB,
    STL,
    PLY,
    DAE,
    BLEND,
    @Suppress("ktlint:standard:enum-entry-name-case")
    `3DS`,
}

@NoArg
data class YouTubeAsset(
    val id: String,
    val width: Int?,
    val height: Int?,
    val previewUrl: String?,
)

@NoArg
data class ImageAssetDto(
    val url: String,
    val width: Int,
    val height: Int,
    val fileName: String?,
    val fileSize: Long?,
)

data class PostDtoListResponse(
    val meta: ListResponseMeta,
    val posts: List<PostDto>,
    val included: PostDtoListIncluded?,
)

data class PostDtoListIncluded(
    val categories: List<CategoryDto> = emptyList(),
    val users: List<UserDto> = emptyList(),
)

fun welcomeMessageId(userId: String) = "welcome-message-$userId"
