package hero.model

import hero.core.annotation.NoArg
import hero.core.data.EntityCollection
import java.time.Instant

@NoArg
data class AppleCharge(
    val appleTransactionId: String,
    val appleReferenceId: String,
    val createdAt: Instant,
    val userId: String,
    val creatorId: String,
    val tierId: String,
    val targetAccountId: String,
    val currencyStore: Currency,
    val storefront: String?,
    /** stripe transfer id to distinguish the transfer has been made */
    val stripeTransferId: String?,
    /** timestamp of the stripe transfer */
    val transferredAt: Instant?,
    /** conversion rate between store currency and tier in cents */
    val conversionRateCents: Long?,
    /** price paid by user in store currency */
    val priceStoreCents: Long,
    /** expected Herohero fee in store currency */
    val priceFeeHeroheroCents: Long,
    /** amount to be transferred to creator's Stripe account in tier currency */
    val transferCents: Long,
    /** stripe customer holding this subscription */
    val customerId: String,
    /** should replicate behaviour of <PERSON>e's `charge.description` */
    val description: String,
) {
    companion object : EntityCollection<AppleCharge> {
        override val collectionName: String = "apple-charges"
    }
}
