package hero.stripe.service

import com.apple.itunes.storekit.signature.JWSSignatureCreator
import hero.jackson.toJson
import java.nio.charset.StandardCharsets
import java.util.Base64

/**
 * Custom signature creator for App Store Connect API.
 *
 * According to discussions in:
 * - https://feedbackassistant.apple.com/feedback/17921525
 * - https://github.com/apple/app-store-server-library-java/issues/169
 *
 * default Apple's `AdvancedCommerceInAppSignatureCreator` cannot be used to sign cancel/revoke requests because:
 *
 * 1. `aud` must (surprisingly) not be `advanced-commerce-api` but `appstoreconnect-v1`
 * 2. The request is missing `exp` claim.
 *
 * Hopefully some day the app-store-server-library above will be able to handle these itself.
 */
class AppStoreConnectSignatureCreator(signingKey: String, keyId: String?, issuerId: String?, bundleId: String?) :
    JWSSignatureCreator(AUDIENCE, signingKey, keyId, issuerId, bundleId) {
    companion object {
        private const val AUDIENCE = "appstoreconnect-v1"
        private const val REQUEST_KEY = "request"
        private const val EXPIRY = "exp"
    }

    /**
     * Create an Advanced Commerce in-app signed request.
     *
     * @see [Generating JWS to sign App Store requests](https://developer.apple.com/documentation/storekit/generating-jws-to-sign-app-store-requests)
     *
     * @param advancedCommerceInAppRequest The request to be signed.
     * @return The signed JWS
     */
    fun createSignature(advancedCommerceInAppRequest: Any): String {
        val jsonRequest = advancedCommerceInAppRequest.toJson()
        val utf8Bytes = jsonRequest.toByteArray(StandardCharsets.UTF_8)
        val encodedRequest = Base64.getEncoder().encodeToString(utf8Bytes)
        return createSignature(
            mapOf(
                REQUEST_KEY to encodedRequest,
                EXPIRY to (System.currentTimeMillis() / 1000 + 300),
            ),
        )
    }
}
