package hero.stripe.service

import com.apple.itunes.storekit.advancedcommerce.AdvancedCommerceInAppSignatureCreator
import com.apple.itunes.storekit.model.AdvancedCommerceInAppRequest
import com.apple.itunes.storekit.model.Environment
import com.apple.itunes.storekit.model.JWSRenewalInfoDecodedPayload
import com.apple.itunes.storekit.model.JWSTransactionDecodedPayload
import com.apple.itunes.storekit.model.ResponseBodyV2DecodedPayload
import com.apple.itunes.storekit.verification.SignedDataVerifier
import hero.jwt.classLoader

/**
 * Generate Apple P8 keys:
 *   In-App Purchases
 *   https://appstoreconnect.apple.com/access/integrations/api/subs
 *
 *   App Store Connect API key:
 *   https://appstoreconnect.apple.com/access/integrations/api
 *
 * Signatures generated with:
 *   https://github.com/apple/app-store-server-library-java
 */
class AppleSigningService(
    private val production: <PERSON>olean,
    environment: String,
    appId: Long = if (production) 6477841374 else 6740578528,
    bundleId: String = if (production) "co.herohero.Herohero" else "co.herohero.devel",
    inAppKeyId: String = "M6N8N6JYFA",
    appConnectKeyId: String = "MKLUSB3F82",
    issuerId: String = "406dba29-8f22-47d5-ab52-dcc6549915cc",
    // Apple tests our production app against SANDBOX environment, which we direct to staging
    appleEnvironment: Environment = if (environment == "prod") Environment.PRODUCTION else Environment.SANDBOX,
    isTest: Boolean = environment == "test",
) {
    private val rootCAs = setOf(
        classLoader.getResourceAsStream("AppleIncRootCertificate.cer"),
        classLoader.getResourceAsStream("AppleRootCA-G2.cer"),
        classLoader.getResourceAsStream("AppleRootCA-G3.cer"),
    )

    private val signedPayloadVerifier = SignedDataVerifier(rootCAs, bundleId, appId, appleEnvironment, !isTest)

    private val appleInAppKeyBytes = classLoader.getResourceAsStream("apple_in_app_key.p8")?.readAllBytes()
        ?: error("Key file not found in resources")

    private val appleAppConnectKeyBytes = classLoader.getResourceAsStream("apple_app_connect_key.p8")?.readAllBytes()
        ?: error("Key file not found in resources")

    private val advancedApiSignatureCreator =
        AdvancedCommerceInAppSignatureCreator(String(appleInAppKeyBytes), inAppKeyId, issuerId, bundleId)

    private val appStoreConnectSignatureCreator =
        AppStoreConnectSignatureCreator(String(appleAppConnectKeyBytes), appConnectKeyId, issuerId, bundleId)

    fun signAdvancedApi(request: AdvancedCommerceInAppRequest): String =
        advancedApiSignatureCreator.createSignature(request)

    fun signAppStoreConnect(request: Any): String = appStoreConnectSignatureCreator.createSignature(request)

    fun decodeNotification(signedPayload: String): ResponseBodyV2DecodedPayload =
        signedPayloadVerifier.verifyAndDecodeNotification(signedPayload)

    fun decodeTransaction(signedPayload: String): JWSTransactionDecodedPayload =
        signedPayloadVerifier.verifyAndDecodeTransaction(signedPayload)

    fun decodeRenewalInfo(signedPayload: String): JWSRenewalInfoDecodedPayload =
        signedPayloadVerifier.verifyAndDecodeRenewalInfo(signedPayload)
}
