package hero.stripe.service

import com.stripe.exception.InvalidRequestException
import com.stripe.model.Account
import com.stripe.model.BalanceTransaction
import com.stripe.model.Charge
import com.stripe.model.Customer
import com.stripe.model.Invoice
import com.stripe.model.LoginLink
import com.stripe.model.PaymentIntent
import com.stripe.model.Payout
import com.stripe.model.Refund
import com.stripe.model.StripeCollection
import com.stripe.model.Subscription
import com.stripe.model.Transfer
import com.stripe.net.RequestOptions
import com.stripe.param.AccountListParams
import com.stripe.param.AccountLoginLinkCreateParams
import com.stripe.param.AccountRejectParams
import com.stripe.param.AccountUpdateParams
import com.stripe.param.AccountUpdateParams.Settings.Payouts
import com.stripe.param.BalanceTransactionListParams
import com.stripe.param.ChargeListParams
import com.stripe.param.ChargeRetrieveParams
import com.stripe.param.ChargeUpdateParams
import com.stripe.param.CustomerCreateParams
import com.stripe.param.CustomerUpdateParams
import com.stripe.param.InvoiceRetrieveParams
import com.stripe.param.PaymentIntentCreateParams
import com.stripe.param.PayoutListParams
import com.stripe.param.RefundRetrieveParams
import com.stripe.param.RefundUpdateParams
import hero.baseutils.SystemEnv
import hero.baseutils.log
import hero.exceptions.http.BadRequestException
import hero.exceptions.http.ForbiddenException
import hero.gcloud.PubSub
import hero.jackson.map
import hero.jackson.parseEnum
import hero.model.Currency
import hero.model.StripeRequirements
import hero.model.User
import hero.model.euroCurrencyRelatedCountries
import hero.model.topics.EmailPublished
import java.time.Instant

class StripeService(
    private val clients: StripeClients,
    private val pubSub: PubSub?,
) {
    fun createCustomer(
        user: User,
        currency: Currency,
    ): Customer {
        val params = CustomerCreateParams.builder()
            // abusing for user.id
            .setDescription(user.id)
            .setName(user.name + " / " + currency)
            .apply {
                user.email?.let { email -> setEmail(email) }
            }
            .putMetadata("currency", currency.name)
            .build()

        return retry {
            clients[currency].customers().create(params)
        }
    }

    fun subscription(
        subscriptionId: String,
        currency: Currency,
    ): Subscription =
        retry {
            clients[currency].subscriptions().retrieve(subscriptionId)
        }

    fun deleteCustomer(
        customerId: String,
        currency: Currency,
        reason: String,
    ) {
        retry {
            try {
                clients[currency]
                    .customers()
                    .retrieve(customerId)
                    .update(CustomerUpdateParams.builder().putMetadata("deletedReason", reason).build())
                    .delete()
            } catch (e: InvalidRequestException) {
                // avoid spamming logs if the customer is already deleted
                if ("No such customer" in (e.message ?: "")) {
                    return@retry
                }
                throw e
            }
        }
    }

    fun listAccounts(currency: Currency): StripeCollection<Account> =
        retry {
            clients[currency].accounts().list(AccountListParams.builder().setLimit(MAX_PERCENT_VALUE).build())
        }

    fun listPayouts(
        accountId: String,
        currency: Currency,
        apply: PayoutListParams.Builder.() -> PayoutListParams.Builder = { this },
    ): StripeCollection<Payout> =
        retry {
            clients[currency].payouts().list(
                PayoutListParams
                    .builder()
                    .let { it.apply() }
                    .build(),
                accountId.stripeAccountRequestOptions(),
            )
        }

    fun getPayout(
        accountId: String,
        payoutId: String,
        currency: Currency,
    ): Payout =
        retry {
            clients[currency].payouts().retrieve(payoutId, accountId.stripeAccountRequestOptions())
        }

    fun getCharge(
        chargeId: String,
        currency: Currency,
    ): Charge =
        retry {
            clients[currency].charges().retrieve(chargeId)
        }

    /** see: https://linear.app/herohero/issue/HH-3217/attach-payout-id-to-transactions-when-paid-out */
    fun markTransactionAsPaidOut(
        transaction: BalanceTransaction,
        accountId: String,
        currency: Currency,
        payoutId: String,
    ) {
        retry {
            val src = transaction.sourceObject!!
            when (src) {
                is Refund -> clients[currency].refunds().update(
                    transaction.source,
                    RefundUpdateParams.builder().putMetadata("payoutId", payoutId).build(),
                    RequestOptions.builder().setStripeAccount(accountId).build(),
                )
                is Charge -> clients[currency].charges().update(
                    transaction.source,
                    ChargeUpdateParams.builder().putMetadata("payoutId", payoutId).build(),
                    RequestOptions.builder().setStripeAccount(accountId).build(),
                )
                else -> {}
            }
        }
    }

    fun markChargeAsSeen(charge: Charge) {
        if (charge.metadata["seen"] != null) {
            return
        }
        try {
            retry {
                charge.update(ChargeUpdateParams.builder().putMetadata("seen", Instant.now().toString()).build())
            }
        } catch (e: InvalidRequestException) {
            log.fatal("Cannot mark ${charge.id} as seen: ${e.message}")
        }
    }

    fun getOriginalChargeByGroup(
        groupId: String,
        currency: Currency,
    ): Charge {
        val charges = clients[currency].charges()
            .list(ChargeListParams.builder().setTransferGroup(groupId).build())
            .data

        if (charges.isEmpty()) {
            error("There was no charge found for group $groupId.")
        }
        if (charges.size > 2) {
            error("There should never be more charges for single group ($groupId): ${charges.map { it.id }}")
        }

        return charges.first()
    }

    fun listPayoutTransactions(
        sourceAccountId: String,
        payoutId: String,
        currency: Currency,
    ): Iterable<BalanceTransaction> =
        clients[currency].balanceTransactions().list(
            BalanceTransactionListParams.builder()
                .setLimit(100)
                .setPayout(payoutId)
                .addAllExpand(
                    listOf(
                        // regular charges
                        "data.source.source_transfer.source_transaction",
                        // refunds
                        "data.source.charge.source_transfer",
                        // Fetching `source_transaction` below would be very helpful for processing
                        // refunds, however 4-level expands are forbidden by Stripe. Therefore,
                        // we need to read these manually.
                        // "data.source.charge.source_transfer.source_transaction",
                    ),
                )
                .build(),
            sourceAccountId
                .stripeAccountRequestOptions(),
        ).autoPagingIterable()

    data class CardDeclinedResponse(
        val message: String?,
        val charge: String?,
        val declineCode: String?,
        val code: String?,
        val param: String?,
        val requestId: String?,
    )

    fun getAccount(
        stripeAccountId: String,
        currency: Currency,
    ): Account =
        retry {
            clients[currency].accounts().retrieve(stripeAccountId)
        }

    fun getLoginLink(
        stripeAccountId: String,
        currency: Currency,
    ): LoginLink =
        retry {
            clients[currency].accounts().loginLinks().create(
                stripeAccountId,
                AccountLoginLinkCreateParams.builder().build(),
                RequestOptions.getDefault(),
            )
        }

    fun requirements(
        user: User,
        source: String,
        currency: Currency,
    ): StripeRequirements {
        if (user.creator.stripeAccountId == null) {
            throw BadRequestException("User does not have yet stripeAccountId associated.")
        }
        val account = getAccount(user.creator.stripeAccountId!!, currency)
        val requirements = account.requirements()
        log.info(
            "Stripe user account verification response from $source, valid: ${requirements.valid}.",
            requirements.map() + mapOf("userId" to user.id),
        )
        return requirements
    }

    fun onboardingFinished(
        user: User,
        currency: Currency,
    ): Boolean = user.creator.stripeAccountId?.let { getAccount(it, currency).detailsSubmitted } ?: false

    // when account has some balance, it cannot be properly deleted nor cancelled –
    // for that reason, we only set manual payouts to "pretend" account to be deleted.
    private fun manualPayouts(
        reason: String,
        deletedBy: String,
    ): AccountUpdateParams =
        AccountUpdateParams.builder()
            .setSettings(
                AccountUpdateParams.Settings.builder().setPayouts(
                    Payouts.builder().setSchedule(
                        Payouts.Schedule.builder().setInterval(Payouts.Schedule.Interval.MANUAL).build(),
                    ).build(),
                ).build(),
            )
            .putMetadata("deletedAt", Instant.now().toString())
            .putMetadata("deletedReason", reason)
            .putMetadata("deletedBy", "https://herohero.co/$deletedBy")
            .build()

    fun rejectAccount(
        accountId: String,
        currency: Currency,
        reason: String,
        deletedBy: String,
    ) {
        retry {
            val account = clients[currency].accounts().retrieve(accountId)
            account.update(manualPayouts(reason, deletedBy))
            account.reject(AccountRejectParams.builder().setReason("terms_of_service").build())
        }
    }

    fun deleteAccount(
        accountId: String,
        currency: Currency,
        reason: String,
        deletedBy: String,
    ) {
        retry {
            val account = clients[currency].accounts().retrieve(accountId)
            account.update(manualPayouts(reason, deletedBy))
            account.delete()
                .let { log.info("Deleted account $accountId with result ${it.deleted}.") }
        }
    }

    fun charges(
        customerId: String,
        currency: Currency,
        startingAfter: String?,
        pageLimit: Long = PAGINATION_LIMIT,
    ): Pair<List<Charge>, Boolean> {
        val params = ChargeListParams.builder()
            .setStartingAfter(startingAfter)
            .setLimit(pageLimit)
            .setCustomer(customerId)
            .addExpand("data.payment_intent.payment_method")
            .build()
        val chargesResponse = clients[currency].charges().list(params)
        val hasNext = chargesResponse.hasMore
        val charges = chargesResponse.data
        return charges.take(pageLimit.toInt()) to hasNext
    }

    fun charge(
        chargeId: String,
        currency: Currency,
        accountId: String? = null,
    ): Charge =
        retry {
            clients[currency].charges().retrieve(
                chargeId,
                ChargeRetrieveParams.builder().addAllExpand(
                    listOf(
                        "source_transfer.source_transaction",
                        "transfer.destination_payment",
                    ),
                ).build(),
                if (accountId != null) RequestOptions.builder().setStripeAccount(accountId).build() else null,
            )
        }

    fun refund(
        refundId: String,
        currency: Currency,
        accountId: String? = null,
    ): Refund =
        retry {
            clients[currency].refunds().retrieve(
                refundId,
                RefundRetrieveParams.builder().addAllExpand(
                    listOf("charge.source_transfer.source_transaction"),
                ).build(),
                if (accountId != null) RequestOptions.builder().setStripeAccount(accountId).build() else null,
            )
        }

    fun customer(
        customerId: String,
        currency: Currency,
    ): Customer =
        retry {
            clients[currency].customers().retrieve(customerId)
        }

    fun invoice(
        invoiceId: String,
        currency: Currency,
        expand: List<String>,
    ): Invoice =
        retry {
            clients[currency].invoices().retrieve(
                invoiceId,
                InvoiceRetrieveParams.builder().addAllExpand(expand).build(),
                RequestOptions.getDefault(),
            )
        }

    fun paymentIntent(
        paymentIntentId: String,
        currency: Currency,
    ): PaymentIntent =
        retry {
            clients[currency].paymentIntents().retrieve(paymentIntentId)
        }

    fun transfer(
        transferId: String,
        currency: Currency,
    ): Transfer =
        retry {
            clients[currency].transfers().retrieve(transferId)
        }

    fun createPaymentIntent(params: PaymentIntentCreateParams): PaymentIntent =
        retry {
            try {
                clients[parseEnum<Currency>(params.currency)!!].paymentIntents().create(params)
            } catch (e: InvalidRequestException) {
                val message = (e.userMessage ?: e.message ?: "") + " [${e.code}]"
                if ("detached from a Customer" in message) {
                    throw ForbiddenException(e.userMessage)
                }
                if ("It's possible this PaymentMethod exists on one of your connected accounts" in message) {
                    throw ForbiddenException(e.userMessage)
                }
                throw e
            }
        }

    fun fraudWarning(
        customerId: String,
        currency: Currency,
        reason: String,
    ) {
        val customer = retry {
            clients[currency].customers().retrieve(customerId)
        }

        log.warn("Warning about potential fraud of $customerId (${customer.name}): $reason")

        if (customer.metadata["fraudWarning"] != null) {
            // we don't want to repetitively spam admins when already notified
            return
        }

        retry {
            customer.update(
                CustomerUpdateParams.builder()
                    .putMetadata("fraudWarning", Instant.now().toString())
                    .build(),
            )
        }

        pubSub?.publish(
            EmailPublished(
                to = if (SystemEnv.isProduction) "<EMAIL>" else "<EMAIL>",
                template = "fraud-warning",
                variables = listOf(
                    "customer-name" to customer.name,
                    "stripe-link" to "https://dashboard.stripe.com/customers/${customer.id}",
                    "reason" to reason,
                ),
                language = "en",
            ),
        )
    }

    fun getHeroheroAccount(currency: Currency): Account = clients[currency].accounts().retrieveCurrent()
}

fun String.stripeAccountRequestOptions(): RequestOptions = RequestOptions.builder().setStripeAccount(this).build()

fun inferOnBehalfOf(
    currency: Currency,
    country: String?,
    stripeAccountIdOnBehalfOf: String?,
) = if (currency == Currency.USD || (country ?: "CZ") !in euroCurrencyRelatedCountries)
    stripeAccountIdOnBehalfOf
else
    null

private const val PAGINATION_LIMIT = 10L

fun <T> nullIfNotFound(block: () -> T): T? =
    try {
        block()
    } catch (e: InvalidRequestException) {
        if ("No such" in e.message!!) {
            null
        } else {
            throw e
        }
    }

fun Account.requirements(): StripeRequirements {
    // since newer Stripe API versions, Stripe decided that Pausing payouts is considered a "disabled reason"
    // which previously was not – so if charges are enabled, we don't consider the account disabled
    val disabledReason = when (requirements.disabledReason) {
        "platform_paused" -> if (chargesEnabled) null else "platform_paused_charges"
        else -> requirements.disabledReason
    }
    return StripeRequirements(
        stripeAccountId = id,
        deleted = deleted == true,
        disabledReason = disabledReason,
        currentlyDue = requirements.currentlyDue ?: emptyList(),
        eventuallyDue = requirements.eventuallyDue ?: emptyList(),
        pastDue = requirements.pastDue ?: emptyList(),
        pendingVerification = requirements.pendingVerification ?: emptyList(),
        errors = requirements.errors?.map { it.code } ?: emptyList(),
    )
}
