package hero.sql

import hero.core.data.Sort
import org.jooq.Field
import org.jooq.Record
import org.jooq.SelectConditionStep
import org.jooq.SelectSeekStep2
import org.jooq.impl.DSL
import org.jooq.impl.DSL.row

fun <T> Field<T>.cmp(
    value: T,
    sort: Sort,
) = when (sort.direction) {
    Sort.Direction.DESC -> this.lt(value)
    Sort.Direction.ASC -> this.gt(value)
}

fun <T> Field<T>.cmpBeforeCursor(
    value: T,
    sort: Sort,
) = when (sort.direction) {
    Sort.Direction.DESC -> this.gt(value)
    Sort.Direction.ASC -> this.lt(value)
}

fun <T> Field<T>.orderBy(sort: Sort) =
    when (sort.direction) {
        Sort.Direction.DESC -> this.desc().nullsLast()
        Sort.Direction.ASC -> this.asc().nullsLast()
    }

fun <T> Field<T>.orderByReversed(sort: Sort) =
    when (sort.direction) {
        Sort.Direction.DESC -> this.asc().nullsLast()
        Sort.Direction.ASC -> this.desc().nullsLast()
    }

fun <R : Record, A, B> SelectConditionStep<R>.tupleSorting(
    fieldA: Field<A>,
    fieldB: Field<B>,
    cursorValueA: A,
    cursorValueB: B,
    sort: Sort,
): SelectSeekStep2<R, A, B> {
    val fieldsTuple = row(fieldA, fieldB)
    val cursorTuple = row(cursorValueA, cursorValueB)
    val cursorCondition = if (sort.direction == Sort.Direction.ASC) {
        fieldsTuple.gt(cursorTuple)
    } else {
        fieldsTuple.lt(cursorTuple)
    }

    return this
        .and(cursorCondition)
        .orderBy(fieldA.orderBy(sort), fieldB.orderBy(sort))
}

fun wordSimilarity(
    query: String,
    field: Field<String>,
) = DSL
    .condition(
        "immutable_unaccent({0}) <% immutable_unaccent({1})",
        query,
        field,
    )
