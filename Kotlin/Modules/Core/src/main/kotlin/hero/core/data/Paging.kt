package hero.core.data

interface Pageable {
    @Deprecated("This will be removed in favor of cursors")
    val pageNumber: Int
    val pageSize: Int

    @Deprecated("This will be removed in favor of cursors")
    val offset: Int
    val beforeCursor: String?
    val afterCursor: String?
    val sort: Sort
}

data class Sort(val by: String? = null, val direction: Direction = Direction.DESC) {
    enum class Direction {
        ASC,
        DESC,
        ;

        fun reversed() =
            if (this == ASC) {
                DESC
            } else {
                ASC
            }
    }

    companion object {
        fun <E : Enum<E>> by(
            value: E?,
            direction: Direction? = Direction.DESC,
        ) = Sort(value?.name, direction ?: Direction.DESC)
    }
}

data class PageRequest(
    @Deprecated("This will be removed in favor of cursors")
    override val pageNumber: Int = 0,
    override val pageSize: Int = 10,
    override val beforeCursor: String? = null,
    override val afterCursor: String? = null,
    override val sort: Sort = Sort(direction = Sort.Direction.DESC),
) : Pageable {
    @Deprecated("This will be removed in favor of cursors")
    override val offset: Int = pageNumber * pageSize
}

data class Page<T>(
    val content: List<T>,
    val nextPageable: Pageable,
    val hasNext: Boolean,
) {
    fun isEmpty(): Boolean = content.isEmpty()

    companion object {
        fun <T> emptyPage(pageSize: Int = 10) = Page<T>(listOf(), PageRequest(pageSize = pageSize), false)
    }
}

interface SimplePageResponse<T> {
    val content: List<T>
    val hasNext: Boolean
    val afterCursor: String?
    val beforeCursor: String?
}

fun <T, R> Page<T>.toResponse(mapper: (T) -> R): SimplePageResponse<R> {
    val mappedContent = content.map(mapper)
    return object : SimplePageResponse<R> {
        override val content: List<R> = mappedContent
        override val hasNext = <EMAIL>
        override val afterCursor = <EMAIL>
        override val beforeCursor = <EMAIL>
    }
}
