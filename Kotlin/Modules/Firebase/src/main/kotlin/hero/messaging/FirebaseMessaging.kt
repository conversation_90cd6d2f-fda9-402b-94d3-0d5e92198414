package hero.messaging

import com.google.firebase.ErrorCode
import com.google.firebase.messaging.AndroidConfig
import com.google.firebase.messaging.AndroidNotification
import com.google.firebase.messaging.ApnsConfig
import com.google.firebase.messaging.Aps
import com.google.firebase.messaging.ApsAlert
import com.google.firebase.messaging.FirebaseMessaging
import com.google.firebase.messaging.MulticastMessage
import hero.common.loadFirebase
import hero.core.logging.Logger
import io.sentry.Sentry

fun initFirebaseMessaging(
    projectId: String,
    envPrefix: String,
): FirebaseMessaging {
    val firebase = loadFirebase(projectId, envPrefix, "messaging")
    return FirebaseMessaging.getInstance(firebase)
}

fun sendMulticastMessage(
    firebaseMessaging: FirebaseMessaging,
    messageBuilder: MulticastMessage.Builder,
    tokens: List<String>,
    userId: String,
    log: Logger,
) {
    val message = messageBuilder.addAllTokens(tokens).build()
    val multicastResponse = firebaseMessaging.sendEachForMulticast(message)
    val retryTokens = mutableListOf<String>()
    multicastResponse.responses.forEachIndexed { index, response ->
        if (response.exception != null && response.exception.errorCode !in setOf(ErrorCode.NOT_FOUND)) {
            if (response.exception.errorCode in retryCodes) {
                retryTokens.add(tokens[index])
            }
        }
    }

    if (retryTokens.isNotEmpty()) {
        log.info("Retrying sending push notification to $retryTokens")
        val retryMulticastResponse = firebaseMessaging.sendEachForMulticast(message)
        retryMulticastResponse.responses
            .mapNotNull { it.exception }
            .forEach {
                if (it.errorCode !in setOf(ErrorCode.NOT_FOUND)) {
                    log.fatal(
                        "Failed to send push notification to $userId even after retrying",
                        cause = it,
                    )
                    // we capture the exception for now, so we can better debug which exceptions are retryable
                    Sentry.captureException(it)
                }
            }
    }
}

fun FirebaseMessaging.sendMulticastMessage(
    tokensFetcher: (String) -> List<String>,
    messageBuilder: () -> MulticastMessage.Builder,
    userId: String,
    logger: Logger,
) {
    val tokens = tokensFetcher(userId)
    if (tokens.isEmpty()) {
        return
    }

    sendMulticastMessage(this, messageBuilder(), tokens, userId, logger)
}

fun buildMessage(
    notificationType: String,
    title: String,
    body: String? = null,
    bodyLocalization: BodyLocalization? = null,
    data: Map<String, String> = emptyMap(),
): MulticastMessage.Builder {
    val allData = buildMap {
        put("notification_type", notificationType)
        data.forEach { (k, value) ->
            put(k, value)
        }
    }
    return MulticastMessage.builder()
        .putAllData(allData)
        .setAndroidConfig(
            AndroidConfig.builder()
                .setNotification(
                    AndroidNotification.builder()
                        .setTitle(title)
                        .setBodyLocalizationKey(bodyLocalization?.key)
                        .setBody(body)
                        .let { an ->
                            bodyLocalization?.args?.forEach {
                                an.addBodyLocalizationArg(it)
                            }
                            an
                        }
                        .build(),
                )
                .build(),
        )
        .setApnsConfig(
            ApnsConfig.builder().setAps(
                Aps.builder()
                    .setAlert(
                        ApsAlert.builder()
                            .setTitle(title)
                            .setLocalizationKey(bodyLocalization?.key)
                            .let { an ->
                                bodyLocalization?.args?.forEach {
                                    an.addLocalizationArg(it)
                                }
                                an
                            }
                            .build(),
                    ).build(),
            ).build(),
        )
}

data class BodyLocalization(
    val key: String,
    val args: List<String> = emptyList(),
)

private val retryCodes = setOf(ErrorCode.UNKNOWN, ErrorCode.INTERNAL, ErrorCode.UNAVAILABLE)
