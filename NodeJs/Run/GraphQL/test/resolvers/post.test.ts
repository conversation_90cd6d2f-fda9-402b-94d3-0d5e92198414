import { comment, post, postAsset, postPreviewAsset, testContext, user } from './test-utils'
import { resolvers } from '../../src/resolvers'
import { GraphQLResolveInfo } from 'graphql/type'
import { PostSortFields, PostVoteType, SortDirection } from '../../src/generated/resolvers-types'

describe('post resolvers', () => {
    describe('resolving post type', () => {
        test.each<{
            fullAssets: boolean
            hasPreview: boolean
            expectedResolveType: string
        }>([
            {
                fullAssets: true,
                hasPreview: false,
                expectedResolveType: 'CompleteContentPost',
            },
            {
                fullAssets: false,
                hasPreview: true,
                expectedResolveType: 'PreviewContentPost',
            },
            {
                fullAssets: false,
                hasPreview: false,
                expectedResolveType: 'LimitedContentPost',
            },
        ])('%s', async ({ fullAssets, hasPreview, expectedResolveType }) => {
            const context = testContext()

            const result = await resolvers.Post!.__resolveType(
                post({ fullAssets, hasPreview }),
                context,
                {} as GraphQLResolveInfo
            )

            expect(result).toEqual(expectedResolveType)
        })
    })

    test('field: user', async () => {
        const context = testContext()
        const expectedUser = user('user-id')
        context.dataSources.userAPI.getUser = jest.fn(async () => expectedUser)

        const result = await resolvers.Post!.user!(post({ userId: 'user-id' }), {}, context, {} as GraphQLResolveInfo)

        expect(context.dataSources.userAPI.getUser).toHaveBeenCalledWith('user-id')
        expect(result).toEqual(expectedUser)
    })

    test('field: posts', async () => {
        const context = testContext()
        const expectedPost = post({ id: 'post-id' })
        const expectedPosts = {
            posts: [expectedPost],
            pagination: {
                hasNextPage: true,
                endCursor: 'end-cursor',
            },
        }
        context.dataSources.postAPI.getPosts = jest.fn(async () => expectedPosts)

        const publishedAt = '2023-05-10T12:12:11.844280Z'
        const result = await resolvers.Post!.posts!(
            post({ userId: 'user-id', publishedAt }),
            { first: 10, direction: SortDirection.DESC },
            context,
            {} as GraphQLResolveInfo
        )

        const expectedCursor = {
            lastPublishedAt: publishedAt,
            '@type': 'GetCreatorPostsPublishedAtCursor',
        }
        const expectedEncodedCursor = Buffer.from(JSON.stringify(expectedCursor)).toString('base64')

        expect(context.dataSources.postAPI.getPosts).toHaveBeenCalledWith(
            'user-id',
            {
                first: 10,
                after: expectedEncodedCursor,
            },
            { by: PostSortFields.PUBLISHED_AT, order: SortDirection.DESC },
            {}
        )
        expect(result).toEqual({
            nodes: [expectedPost],
            pageInfo: {
                hasNextPage: true,
                endCursor: 'end-cursor',
            },
        })
    })

    describe('resolving post assets type', () => {
        test.each<{
            assetType: 'document' | 'image' | 'gjirafa' | 'gjirafa-livestream' | 'empty'
            expectedResolveType: string
        }>([
            {
                assetType: 'document',
                expectedResolveType: 'PostDocumentAsset',
            },
            {
                assetType: 'image',
                expectedResolveType: 'PostImageAsset',
            },
            {
                assetType: 'gjirafa',
                expectedResolveType: 'PostGjirafaAsset',
            },
            {
                assetType: 'gjirafa-livestream',
                expectedResolveType: 'PostGjirafaLivestreamAsset',
            },
            {
                assetType: 'empty',
                expectedResolveType: 'PostEmptyAsset',
            },
        ])('%s', async ({ assetType, expectedResolveType }) => {
            const context = testContext()

            const result = await resolvers.PostAsset!.__resolveType(
                postAsset(assetType),
                context,
                {} as GraphQLResolveInfo
            )

            expect(result).toEqual(expectedResolveType)
        })
    })

    describe('resolving post preview assets type', () => {
        test.each<{
            assetType: 'preview-image' | 'preview-gjirafa' | 'preview-document'
            expectedResolveType: string
        }>([
            {
                assetType: 'preview-image',
                expectedResolveType: 'PostPreviewImageAsset',
            },
            {
                assetType: 'preview-gjirafa',
                expectedResolveType: 'PostPreviewGjirafaAsset',
            },
            {
                assetType: 'preview-document',
                expectedResolveType: 'PostPreviewDocumentAsset',
            },
        ])('%s', async ({ assetType, expectedResolveType }) => {
            const context = testContext()

            const result = await resolvers.PostPreviewAsset!.__resolveType(
                postPreviewAsset(assetType),
                context,
                {} as GraphQLResolveInfo
            )

            expect(result).toEqual(expectedResolveType)
        })
    })

    describe('CompleteContentPost resolvers', () => {
        test('field: comments', async () => {
            const context = testContext()
            const expectedComment = comment()
            const expectedComments = {
                comments: [expectedComment],
                pagination: {
                    hasNextPage: true,
                },
            }
            context.dataSources.postAPI.getComments = jest.fn(async () => expectedComments)

            const result = await resolvers.CompleteContentPost!.comments!(
                post({ id: 'post-id' }),
                { first: 10, sortDirection: SortDirection.ASC },
                context,
                {} as GraphQLResolveInfo
            )

            expect(context.dataSources.postAPI.getComments).toHaveBeenCalledWith('post-id', {
                first: 10,
                sortDirection: SortDirection.ASC,
            })
            expect(result).toEqual({
                nodes: [expectedComment],
                pageInfo: {
                    hasNextPage: true,
                },
            })
        })
        describe('field: myVote', () => {
            test.each<{
                voteValue: number
                expectedVoteType: PostVoteType
            }>([
                {
                    voteValue: -1,
                    expectedVoteType: PostVoteType.DOWN,
                },
                {
                    voteValue: 0,
                    expectedVoteType: PostVoteType.NONE,
                },
                {
                    voteValue: 1,
                    expectedVoteType: PostVoteType.UP,
                },
            ])('%s', async ({ voteValue, expectedVoteType }) => {
                const context = testContext()

                const result = await resolvers.CompleteContentPost!.myVote!(
                    post({ myVote: voteValue }),
                    {},
                    context,
                    {} as GraphQLResolveInfo
                )

                expect(result).toEqual(expectedVoteType)
            })
        })
    })
})
