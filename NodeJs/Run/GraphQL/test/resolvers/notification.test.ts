import { message, notification, post, testContext, user } from './test-utils'
import { GraphQLResolveInfo } from 'graphql/type'
import { resolvers } from '../../src/resolvers'
import { StorageEntityType } from '../../src/generated/api'
import { NotificationType } from '../../src/models/notification'
import { GraphQLError } from 'graphql/error'

describe('notification resolvers', () => {
    describe('resolving notification type', () => {
        test.each<{
            type: NotificationType
            expectedResolveType: string
            communityId?: string
            objectType?: StorageEntityType
        }>([
            {
                type: NotificationType.NEW_THREAD,
                expectedResolveType: 'CommunityThreadNotification',
            },
            {
                type: NotificationType.NEW_POST,
                expectedResolveType: 'PostNotification',
            },
            {
                type: NotificationType.NEW_LIVESTREAM,
                expectedResolveType: 'PostNotification',
            },
            {
                type: NotificationType.NEW_SUBSCRIPTION,
                expectedResolveType: 'SubscriberNotification',
            },
            {
                type: NotificationType.SUBSCRIBE_REQUEST_ACCEPTED,
                expectedResolveType: 'RequestNotification',
            },
            {
                type: NotificationType.CANCELLED_SUBSCRIPTION_INSUFFICIENT_FUNDS,
                expectedResolveType: 'SubscriptionNotification',
            },
            {
                type: NotificationType.CANCELLED_SUBSCRIPTION_ENDED,
                expectedResolveType: 'SubscriptionNotification',
            },
            {
                type: NotificationType.CANCELLED_SUBSCRIPTION_REFUSED,
                expectedResolveType: 'SubscriptionNotification',
            },
            {
                type: NotificationType.CANCELLED_SUBSCRIPTION_REFUNDED,
                expectedResolveType: 'SubscriptionNotification',
            },
            {
                type: NotificationType.CANCELLED_SUBSCRIPTION_BY_CREATOR,
                expectedResolveType: 'SubscriptionNotification',
            },
            {
                type: NotificationType.CANCELLED_SUBSCRIPTION_OTHER,
                expectedResolveType: 'SubscriptionNotification',
            },
            {
                type: NotificationType.PAYMENT_FAILED,
                expectedResolveType: 'SubscriptionNotification',
                objectType: StorageEntityType.USER,
            },
            {
                type: NotificationType.PAYMENT_CARD_DECLINED,
                expectedResolveType: 'SubscriptionNotification',
                objectType: StorageEntityType.USER,
            },
            {
                type: NotificationType.PAYMENT_INSUFFICIENT_FUNDS,
                expectedResolveType: 'SubscriptionNotification',
                objectType: StorageEntityType.USER,
            },
            {
                type: NotificationType.NEW_COMMENT,
                expectedResolveType: 'CommentNotification',
            },
            {
                type: NotificationType.NEW_COMMENT,
                communityId: '65d81d35-ad60-41fa-b32d-c8cb41e55d89',
                expectedResolveType: 'CommunityCommentNotification',
            },
            {
                type: NotificationType.NEW_REPLY,
                expectedResolveType: 'CommentNotification',
            },
            {
                type: NotificationType.NEW_REPLY,
                communityId: '65d81d35-ad60-41fa-b32d-c8cb41e55d89',
                expectedResolveType: 'CommunityCommentNotification',
            },
            {
                type: NotificationType.NEW_REPLY_TO_REPLY,
                expectedResolveType: 'CommentNotification',
            },
            {
                type: NotificationType.NEW_REPLY_TO_REPLY,
                communityId: '65d81d35-ad60-41fa-b32d-c8cb41e55d89',
                expectedResolveType: 'CommunityCommentNotification',
            },
            {
                type: NotificationType.PAID_POST,
                expectedResolveType: 'MessageNotification',
            },
        ])('%s', async ({ type, expectedResolveType, objectType, communityId }) => {
            const context = testContext()

            const result = await resolvers.Notification!.__resolveType(
                notification({ type, objectType, communityId }),
                context,
                {} as GraphQLResolveInfo
            )

            expect(result).toEqual(expectedResolveType)
        })
    })

    describe('PostNotification resolvers', () => {
        test('field: post', async () => {
            const context = testContext()
            const expectedPost = post({ id: 'post-id' })
            context.dataSources.postAPI.getPost = jest.fn(async () => expectedPost)

            const result = await resolvers.PostNotification!.post!(
                notification({ objectId: 'post-id' }),
                {},
                context,
                {} as GraphQLResolveInfo
            )

            expect(result).toEqual(expectedPost)
            expect(context.dataSources.postAPI.getPost).toHaveBeenCalledWith('post-id')
        })

        describe('field: creator', () => {
            test('lastActor prefetched, should not call API', async () => {
                const context = testContext()
                const expectedUser = user('user-id')
                context.dataSources.userAPI.getUserOrDeleted = jest.fn()

                const result = await resolvers.PostNotification!.creator!(
                    notification({ objectId: 'user-id', lastActor: expectedUser }),
                    {},
                    context,
                    {} as GraphQLResolveInfo
                )

                expect(result).toEqual(expectedUser)
                expect(context.dataSources.userAPI.getUserOrDeleted).not.toHaveBeenCalled()
            })

            test('missing lastActor, API returns active user', async () => {
                const context = testContext()
                const expectedUser = user('user-id')
                context.dataSources.userAPI.getUserOrDeleted = jest.fn(async () => expectedUser)

                const result = await resolvers.PostNotification!.creator!(
                    notification({ objectId: 'user-id', lastActorId: 'user-id' }),
                    {},
                    context,
                    {} as GraphQLResolveInfo
                )

                expect(result).toEqual(expectedUser)
                expect(context.dataSources.userAPI.getUserOrDeleted).toHaveBeenCalledWith('user-id')
            })
        })
    })

    describe('SubscriberNotification resolvers', () => {
        describe('field: lastSubscriber', () => {
            test('lastActor prefetched, should not call API', async () => {
                const context = testContext()
                const expectedUser = user('user-id')
                context.dataSources.userAPI.getUser = jest.fn()

                const result = await resolvers.SubscriberNotification!.lastSubscriber!(
                    notification({ objectId: 'user-id', lastActor: expectedUser }),
                    {},
                    context,
                    {} as GraphQLResolveInfo
                )

                expect(result).toEqual(expectedUser)
                expect(context.dataSources.userAPI.getUser).not.toHaveBeenCalled()
            })

            test('missing lastActor, API returns active user', async () => {
                const context = testContext()
                const expectedUser = user('last-actor-id')
                context.dataSources.userAPI.getUserOrDeleted = jest.fn(async () => expectedUser)

                const result = await resolvers.SubscriberNotification!.lastSubscriber!(
                    notification({ objectId: 'user-id', lastActorId: 'last-actor-id' }),
                    {},
                    context,
                    {} as GraphQLResolveInfo
                )

                expect(result).toEqual(expectedUser)
                expect(context.dataSources.userAPI.getUserOrDeleted).toHaveBeenCalledWith('last-actor-id')
            })
        })
    })

    describe('CommentNotification resolvers', () => {
        describe('field: commenter', () => {
            test('lastActor prefetched, should not call API', async () => {
                const context = testContext()
                const expectedUser = user('user-id')
                context.dataSources.userAPI.getUser = jest.fn()

                const result = await resolvers.CommentNotification!.commenter!(
                    notification({ objectId: 'user-id', lastActor: expectedUser }),
                    {},
                    context,
                    {} as GraphQLResolveInfo
                )

                expect(result).toEqual(expectedUser)
                expect(context.dataSources.userAPI.getUser).not.toHaveBeenCalled()
            })

            test('missing lastActor, API returns active user', async () => {
                const context = testContext()
                const expectedUser = user('last-actor-id')
                context.dataSources.userAPI.getUserOrDeleted = jest.fn(async () => expectedUser)

                const result = await resolvers.CommentNotification!.commenter!(
                    notification({ objectId: 'user-id', lastActorId: 'last-actor-id' }),
                    {},
                    context,
                    {} as GraphQLResolveInfo
                )

                expect(result).toEqual(expectedUser)
                expect(context.dataSources.userAPI.getUserOrDeleted).toHaveBeenCalledWith('last-actor-id')
            })
        })

        describe('field: postId', () => {
            test('should parse post id from comment', async () => {
                const result = resolvers.CommentNotification!.postId!(
                    notification({ objectId: 'hunghoangzfgvmdemdlfutxjpvxtuvqsqeda-nxtikfpl' }),
                    {},
                    testContext({ userId: 'hunghoangzfgvmdemdlfutx' }),
                    {} as GraphQLResolveInfo
                )

                expect(result).toEqual('hunghoangzfgvmdemdlfutxjpvxtuvqsqeda')
            })

            test('should parse post id from reply', async () => {
                const result = resolvers.CommentNotification!.postId!(
                    notification({ objectId: 'hunghoangzfgvmdemdlfutxjpvxtuvqsqeda-nxtikfpl-wpnazxpm' }),
                    {},
                    testContext({ userId: 'hunghoangzfgvmdemdlfutx' }),
                    {} as GraphQLResolveInfo
                )

                expect(result).toEqual('hunghoangzfgvmdemdlfutxjpvxtuvqsqeda')
            })

            test('should parse post id from comment - userId with multiple hyphens', async () => {
                const result = resolvers.CommentNotification!.postId!(
                    notification({ objectId: 'kny-voj-jhswcqwgxjagzssigighmprcoqgjcdqzcfuosbfq-cmhezcgx' }),
                    {},
                    testContext({ userId: 'kny-voj-jhswcqwgxjagzssigig' }),
                    {} as GraphQLResolveInfo
                )

                expect(result).toEqual('kny-voj-jhswcqwgxjagzssigighmprcoqgjcdqzcfuosbfq')
            })

            test('should parse post id from reply - userId with multiple hyphens', async () => {
                const result = resolvers.CommentNotification!.postId!(
                    notification({ objectId: 'kny-voj-jhswcqwgxjagzssigighmprcoqgjcdqzcfuosbfq-cmhezcgx-kejnwjq' }),
                    {},
                    testContext({ userId: 'kny-voj-jhswcqwgxjagzssigig' }),
                    {} as GraphQLResolveInfo
                )

                expect(result).toEqual('kny-voj-jhswcqwgxjagzssigighmprcoqgjcdqzcfuosbfq')
            })

            test('should throw if object id is in incorrect format', async () => {
                await expect(async () => {
                    await resolvers.CommentNotification!.postId!(
                        notification({ objectId: 'hunghoangzfgvmdemdlfutxjpvxtuvqsqeda' }),
                        {},
                        testContext(),
                        {} as GraphQLResolveInfo
                    )
                }).rejects.toBeInstanceOf(GraphQLError)
            })

            test('should throw if object id is missing', async () => {
                await expect(async () => {
                    await resolvers.CommentNotification!.postId!(
                        notification(),
                        {},
                        testContext(),
                        {} as GraphQLResolveInfo
                    )
                }).rejects.toBeInstanceOf(GraphQLError)
            })
        })

        describe('field: post', () => {
            test('should parse the post id and fetch it from the data source', async () => {
                const context = testContext({ userId: 'hunghoangzfgvmdemdlfutxjpvxtuvqsqeda' })
                const expectedPost = post({ id: 'hunghoangzfgvmdemdlfutxjpvxtuvqsqeda' })
                context.dataSources.postAPI.getPost = jest.fn(async () => expectedPost)

                const result = await resolvers.CommentNotification!.post!(
                    notification({ objectId: 'hunghoangzfgvmdemdlfutxjpvxtuvqsqeda-nxtikfpl' }),
                    {},
                    context,
                    {} as GraphQLResolveInfo
                )

                expect(result).toEqual(expectedPost)
                expect(context.dataSources.postAPI.getPost).toHaveBeenCalledWith('hunghoangzfgvmdemdlfutxjpvxtuvqsqeda')
            })

            test('should parse the post id and fetch it from the data source - user multiple hyphens', async () => {
                const context = testContext({ userId: 'kny-voj-jhswcqwgxjagzssigig' })
                const expectedPost = post({ id: 'kny-voj-jhswcqwgxjagzssigighmprcoqgjcdqzcfuosbfq' })
                context.dataSources.postAPI.getPost = jest.fn(async () => expectedPost)

                const result = await resolvers.CommentNotification!.post!(
                    notification({ objectId: 'kny-voj-jhswcqwgxjagzssigighmprcoqgjcdqzcfuosbfq-cmhezcgx' }),
                    {},
                    context,
                    {} as GraphQLResolveInfo
                )

                expect(result).toEqual(expectedPost)
                expect(context.dataSources.postAPI.getPost).toHaveBeenCalledWith(
                    'kny-voj-jhswcqwgxjagzssigighmprcoqgjcdqzcfuosbfq'
                )
            })
        })
    })

    describe('SubscriptionNotification resolvers', () => {
        describe('field: creator', () => {
            test('API returns active creator', async () => {
                const context = testContext()
                const expectedUser = user('user-id')
                context.dataSources.userAPI.getUserOrDeleted = jest.fn(async () => expectedUser)

                const result = await resolvers.SubscriptionNotification!.creator!(
                    notification({
                        objectId: 'user-id',
                        lastActor: user('random-last-actor'),
                        type: NotificationType.PAYMENT_FAILED,
                    }),
                    {},
                    context,
                    {} as GraphQLResolveInfo
                )

                expect(result).toEqual(expectedUser)
                expect(context.dataSources.userAPI.getUserOrDeleted).toHaveBeenCalledWith('user-id')
            })
        })
    })

    describe('MessageNotification resolvers', () => {
        describe('field: user', () => {
            test('lastActor prefetched, should not call API', async () => {
                const context = testContext()
                const expectedUser = user('user-id')
                context.dataSources.userAPI.getUserOrDeleted = jest.fn()

                const result = await resolvers.MessageNotification!.user!(
                    notification({ objectId: 'user-id', lastActor: expectedUser }),
                    {},
                    context,
                    {} as GraphQLResolveInfo
                )

                expect(result).toEqual(expectedUser)
                expect(context.dataSources.userAPI.getUserOrDeleted).not.toHaveBeenCalled()
            })

            test('missing lastActor, API returns active user', async () => {
                const context = testContext()
                const expectedUser = user('last-actor-id')
                context.dataSources.userAPI.getUserOrDeleted = jest.fn(async () => expectedUser)

                const result = await resolvers.MessageNotification!.user!(
                    notification({ objectId: 'user-id', lastActorId: 'last-actor-id' }),
                    {},
                    context,
                    {} as GraphQLResolveInfo
                )

                expect(result).toEqual(expectedUser)
                expect(context.dataSources.userAPI.getUserOrDeleted).toHaveBeenCalledWith('last-actor-id')
            })
        })
        describe('field: message', () => {
            test('should fetch message', async () => {
                const context = testContext()
                const expectedMessage = message('message-id')
                context.dataSources.messageThreadAPI.getMessage = jest.fn(async () => expectedMessage)

                const result = await resolvers.MessageNotification!.message!(
                    notification({ objectId: 'message-id', lastActorId: 'last-actor-id' }),
                    {},
                    context,
                    {} as GraphQLResolveInfo
                )

                expect(result).toEqual(expectedMessage)
                expect(context.dataSources.messageThreadAPI.getMessage).toHaveBeenCalledWith('message-id')
            })
        })

        describe('field: messageThreadId', () => {
            test('should parse message thread id from the message id', async () => {
                const result = resolvers.MessageNotification!.messageThreadId!(
                    notification({ objectId: '1638447277322-1076589356-1638447279661-ggoozpkscwihisvxg' }),
                    {},
                    testContext(),
                    {} as GraphQLResolveInfo
                )

                expect(result).toEqual('1638447277322-1076589356')
            })

            test('should throw if object id is in incorrect format', async () => {
                await expect(async () => {
                    await resolvers.MessageNotification!.messageThreadId!(
                        // missing last part of the id
                        notification({ objectId: '1638447277322-1076589356-1638447279661' }),
                        {},
                        testContext(),
                        {} as GraphQLResolveInfo
                    )
                }).rejects.toBeInstanceOf(GraphQLError)
            })

            test('should throw if object id is missing', async () => {
                await expect(async () => {
                    await resolvers.MessageNotification!.messageThreadId!(
                        notification(),
                        {},
                        testContext(),
                        {} as GraphQLResolveInfo
                    )
                }).rejects.toBeInstanceOf(GraphQLError)
            })
        })
    })
})
