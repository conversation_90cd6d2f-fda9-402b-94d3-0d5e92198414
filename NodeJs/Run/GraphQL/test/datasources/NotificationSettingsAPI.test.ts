import nock from 'nock'
import { Environment } from '../../src/common/environment'
import { NotificationSettingsAPI } from '../../src/datasources/NotificationSettingsAPI'
import { NotificationSettingsResponse } from '../../src/generated/api'

beforeEach(() => {
    nock.disableNetConnect()
})

describe('data source: NotificationSettingsAPI', () => {
    describe('method: getNotificationSettings', () => {
        test('should make a call to fetch currently logged in users notification settings', async () => {
            const userId = 'user-id'
            const underTest = new NotificationSettingsAPI(Environment.DEVEL, userId)

            const response: NotificationSettingsResponse = {
                emailNewDm: true,
                emailNewPost: false,
                newsletter: true,
                termsChanged: false,
                pushNewPost: true,
                pushNewComment: false,
                pushNewMessage: true,
            }
            const scope = nock('https://devel-api-90568653510.europe-west1.run.app')
                .get(`/v1/notification-settings`)
                .reply(200, response)

            const notificationSettings = await underTest.getNotificationSettings()

            expect(notificationSettings).toEqual({
                emailNewDm: true,
                emailNewPost: false,
                newsletter: true,
                termsChanged: false,
                pushNewPost: true,
                pushNewComment: false,
                pushNewMessage: true,
            })

            expect(scope.isDone()).toBeTruthy()
        })
    })

    describe('method: updateNotificationSettings', () => {
        test('should make a call to update notification settings', async () => {
            const underTest = new NotificationSettingsAPI(Environment.DEVEL, 'user-id')

            const scope = nock('https://devel-api-90568653510.europe-west1.run.app')
                .put(`/v1/notification-settings`, {
                    emailNewDm: true,
                    emailNewPost: false,
                    newsletter: true,
                    termsChanged: false,
                    pushNewPost: true,
                    pushNewComment: false,
                    pushNewMessage: true,
                })
                .reply(200)

            await underTest.updateNotificationSettings({
                emailNewDm: true,
                emailNewPost: false,
                newsletter: true,
                termsChanged: false,
                pushNewPost: true,
                pushNewComment: false,
                pushNewMessage: true,
            })

            expect(scope.isDone()).toBeTruthy()
        })
    })
})
