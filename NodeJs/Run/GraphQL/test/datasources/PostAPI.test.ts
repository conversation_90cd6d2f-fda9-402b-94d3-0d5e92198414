import { PostAPI } from '../../src/datasources/PostAPI'
import { Environment } from '../../src/common/environment'
import { CommentResponse, DocumentType, PostDto, PostState as RestPostState } from '../../src/generated/api'
import {
    GjirafaLivestreamStatus,
    GjirafaQualityTypeStatus,
    PostDocumentType,
    PostPreviewGjirafaType,
    PostSortFields,
    PostSource,
    PostState,
    PostState as PostModelState,
    PostTypeFilter,
    SortDirection,
} from '../../src/generated/resolvers-types'
import { CommentModel, PollModel, PostModel } from '../../src/models/post'
import { pagedPostResponse, pollResponse, postResponse } from './test-utils'
import { PaginationModel } from '../../src/models/pagination'
import { GraphQLError } from 'graphql/error'
import nock from 'nock'

beforeEach(() => {
    nock.disableNetConnect()
})

describe('data source: PostAPI', () => {
    describe('method: getPost', () => {
        test('should make a call to get a single creator post and correctly map it', async () => {
            // given
            const underTest = new PostAPI(Environment.DEVEL)
            const postId = 'post-id'
            const scope = nock('https://devel-api-90568653510.europe-west1.run.app')
                .get(`/v4/posts/${postId}`)
                .reply(200, postResponse({ savedPostInfo: true }))

            // when
            const post = await underTest.getPost(postId)

            // then
            expect(post).toEqual<PostModel>(expectedPostModel)
            expect(scope.isDone()).toBeTruthy()
        })
    })

    describe('method: getPoll', () => {
        test('should make a call to get a poll and correctly map it', async () => {
            // given
            const underTest = new PostAPI(Environment.DEVEL)
            const pollId = 'poll-id'
            const scope = nock('https://devel-api-90568653510.europe-west1.run.app')
                .get(`/v1/polls/${pollId}`)
                .reply(200, pollResponse())

            // when
            const poll = await underTest.getPoll(pollId)

            // then
            expect(poll).toEqual<PollModel>(expectedPollModel)
            expect(scope.isDone()).toBeTruthy()
        })
    })

    describe('method: postVotes', () => {
        test('should make a call to cast votes on a poll', async () => {
            // given
            const underTest = new PostAPI(Environment.DEVEL)
            const pollId = 'poll-id'
            const votes = ['option-1', 'option-2']
            const scope = nock('https://devel-api-90568653510.europe-west1.run.app')
                .post(`/v1/polls/${pollId}/votes`, (body) => {
                    return (
                        JSON.stringify(body) ===
                        JSON.stringify({
                            votes: [{ optionId: 'option-1' }, { optionId: 'option-2' }],
                        })
                    )
                })
                .reply(200)

            // when
            await underTest.postVotes(pollId, votes)

            // then
            expect(scope.isDone()).toBeTruthy()
        })
    })

    describe('method: postPostVotes', () => {
        test('should make a call to cast an upvote on a post', async () => {
            // given
            const underTest = new PostAPI(Environment.DEVEL)
            const postId = 'post-id'
            const voteValue = 1
            const scope = nock('https://devel-api-90568653510.europe-west1.run.app')
                .post(`/v4/posts/${postId}/votes`, (body) => {
                    return (
                        JSON.stringify(body) ===
                        JSON.stringify({
                            voteValue: 1,
                        })
                    )
                })
                .reply(200)

            // when
            await underTest.postPostVotes(postId, voteValue)

            // then
            expect(scope.isDone()).toBeTruthy()
        })
    })

    describe('method: putPoll', () => {
        test('should make a call to update poll', async () => {
            // given
            const underTest = new PostAPI(Environment.DEVEL)
            const pollId = 'poll-id'
            const scope = nock('https://devel-api-90568653510.europe-west1.run.app')
                .put(`/v1/polls/${pollId}`, (body) => {
                    return (
                        JSON.stringify(body) ===
                        JSON.stringify({
                            hasEnded: true,
                        })
                    )
                })
                .reply(200)

            // when
            await underTest.putPoll(pollId)

            // then
            expect(scope.isDone()).toBeTruthy()
        })
    })

    describe('method: getPosts', () => {
        test('should make a call to get creator posts correctly map it', async () => {
            // given
            const underTest = new PostAPI(Environment.DEVEL)
            const creatorId = 'creator-id'
            const scope = nock('https://devel-api-90568653510.europe-west1.run.app')
                .post(`/v3/posts/search`, (body) => {
                    return (
                        JSON.stringify(body) ===
                        JSON.stringify({
                            sortBy: 'PINNED_AT',
                            filter: {
                                type: 'IN_PROGRESS',
                                categoryId: 'category-id',
                                communityId: '20ff3aba-123c-4c4d-8b2c-f193a62a0df7',
                                excludedCreatorIds: ['excluded-creator-id'],
                                source: 'SUBSCRIBED_CREATORS',
                            },
                            pageSize: 10,
                            creatorId: 'creator-id',
                            sortDirection: 'DESC',
                            afterCursor: 'cursor',
                        })
                    )
                })
                .reply(200, pagedPostResponse({ savedPostInfo: true }))

            // when
            const { posts, pagination } = await underTest.getPosts(
                creatorId,
                {
                    first: 10,
                    after: 'cursor',
                },
                { by: PostSortFields.PINNED_AT, order: SortDirection.DESC },
                {
                    categoryId: 'category-id',
                    type: PostTypeFilter.IN_PROGRESS,
                    communityId: '20ff3aba-123c-4c4d-8b2c-f193a62a0df7',
                    excludedCreatorIds: ['excluded-creator-id'],
                    source: PostSource.SUBSCRIBED_CREATORS,
                }
            )

            // then
            expect(posts).toEqual<PostModel[]>([expectedPostModel])
            expect(pagination).toEqual<PaginationModel>({
                hasNextPage: true,
                endCursor: 'eyJ1c2VySWQiOiJodW5naG9hbmd6Zmd2bWRlbSIsImxhc3RNZXNzYWdlQXQiOiIyMD',
            })
            expect(scope.isDone()).toBeTruthy()
        })
    })

    describe('method: getPostsJsonAPI', () => {
        test('should make a call to get posts from json api correctly map it', async () => {
            // given
            const underTest = new PostAPI(Environment.DEVEL)
            const scope = nock('https://devel-api-90568653510.europe-west1.run.app')
                .get(`/v2/posts`)
                .query({ query: 'query' })
                .reply(200, { posts: [postDto], meta: { hasNext: false } })

            // when
            const { posts, pagination } = await underTest.getPostsJsonAPI({ query: 'query' })

            // then
            expect(posts).toEqual<PostModel[]>([
                {
                    ...expectedPostModel,
                    categories: [],
                    assets: [],
                    savedPostInfo: undefined,
                    previewAssets: [],
                    title: undefined,
                    hasPreviewInternal: false,
                },
            ])
            expect(pagination).toEqual<PaginationModel>({ hasNextPage: false })
            expect(scope.isDone()).toBeTruthy()
        })
    })

    describe('method: getLivestreams', () => {
        test('should make a call to get creator livestreams correctly map it', async () => {
            // given
            const underTest = new PostAPI(Environment.DEVEL)
            const creatorId = 'creator-id'
            const scope = nock('https://devel-api-90568653510.europe-west1.run.app')
                .get(`/v1/users/${creatorId}/livestreams`)
                .reply(200, pagedPostResponse({ savedPostInfo: true }))

            // when
            const { posts, pagination } = await underTest.getLivestreams(creatorId)

            // then
            expect(posts).toEqual<PostModel[]>([expectedPostModel])
            expect(pagination).toEqual<PaginationModel>({
                hasNextPage: true,
                endCursor: 'eyJ1c2VySWQiOiJodW5naG9hbmd6Zmd2bWRlbSIsImxhc3RNZXNzYWdlQXQiOiIyMD',
            })
            expect(scope.isDone()).toBeTruthy()
        })
    })

    describe('method: getComments', () => {
        test('should make a call to get comments and correctly map it', async () => {
            // given
            const underTest = new PostAPI(Environment.DEVEL)
            const postId = 'post-id'
            const scope = nock('https://devel-api-90568653510.europe-west1.run.app')
                .get(`/v4/posts/${postId}/comments`)
                .query({ pageSize: 10, afterCursor: 'afterCursor', sort: 'DESC' })
                .reply(200, pagedPostResponse({ parentId: 'parent-id' }))

            // when
            const { comments, pagination } = await underTest.getComments(postId, {
                first: 10,
                after: 'afterCursor',
                sortDirection: SortDirection.DESC,
            })

            // then
            expect(comments).toEqual<CommentModel[]>([expectedCommentModel])
            expect(pagination).toEqual<PaginationModel>({
                hasNextPage: true,
                endCursor: 'eyJ1c2VySWQiOiJodW5naG9hbmd6Zmd2bWRlbSIsImxhc3RNZXNzYWdlQXQiOiIyMD',
            })
            expect(scope.isDone()).toBeTruthy()
        })
    })

    describe('method: createComment', () => {
        test('should make a call to create a comment and correctly map the response', async () => {
            // given
            const underTest = new PostAPI(Environment.DEVEL)
            const scope = nock('https://devel-api-90568653510.europe-west1.run.app')
                .post(`/v1/comments`, (body) => {
                    return (
                        JSON.stringify(body) ===
                        JSON.stringify({
                            parentId: 'parent-id',
                            attributes: {
                                text: 'text',
                                textHtml: 'text-html',
                                textDelta: '{}',
                                assets: [
                                    {
                                        image: {
                                            width: 512,
                                            height: 513,
                                            url: 'url',
                                            hidden: false,
                                        },
                                    },
                                ],
                            },
                        })
                    )
                })
                .reply(200, postResponse({ parentId: 'parent-id' }))

            // when
            const attributes = {
                text: 'text',
                textHtml: 'text-html',
                textDelta: '{}',
                assets: [
                    {
                        image: {
                            width: 512,
                            height: 513,
                            url: 'url',
                        },
                    },
                ],
            }
            const comment = await underTest.createComment('parent-id', attributes)

            // then
            expect(comment).toEqual(expectedCommentModel)
            expect(scope.isDone()).toBeTruthy()
        })

        test('should throw when asset more than 1 defined assets', async () => {
            // given
            const underTest = new PostAPI(Environment.DEVEL)
            const attributes = {
                text: 'text',
                textHtml: 'text-html',
                textDelta: '{}',
                publishedAt: '2024-02-23T13:55:30.311615168Z',
                assets: [
                    {
                        image: {
                            width: 512,
                            height: 513,
                            url: 'url',
                        },
                        document: {
                            type: PostDocumentType.DOCX,
                            name: 'document-name',
                            url: 'url',
                        },
                    },
                ],
            }

            await expect(async () => {
                await underTest.createComment('parent-id', attributes)
            }).rejects.toBeInstanceOf(GraphQLError)
        })

        test('should throw when asset has no defined assets', async () => {
            // given
            const underTest = new PostAPI(Environment.DEVEL)
            const attributes = {
                text: 'text',
                textHtml: 'text-html',
                publishedAt: '2024-02-23T13:55:30.311615168Z',
                assets: [{}],
            }

            await expect(async () => {
                await underTest.createComment('parent-id', attributes)
            }).rejects.toBeInstanceOf(GraphQLError)
        })
    })

    describe('method: updateComment', () => {
        test('should make a call to update a comment and correctly map the response', async () => {
            // given
            const underTest = new PostAPI(Environment.DEVEL)
            const scope = nock('https://devel-api-90568653510.europe-west1.run.app')
                .put(`/v1/comments/comment-id`, (body) => {
                    return (
                        JSON.stringify(body) ===
                        JSON.stringify({
                            attributes: {
                                text: 'text',
                                textHtml: 'text-html',
                                textDelta: '{}',
                                assets: [
                                    {
                                        image: {
                                            width: 512,
                                            height: 513,
                                            url: 'url',
                                            hidden: false,
                                        },
                                    },
                                ],
                            },
                        })
                    )
                })
                .reply(200, postResponse({ parentId: 'parent-id' }))

            // when
            const attributes = {
                text: 'text',
                textHtml: 'text-html',
                textDelta: '{}',
                assets: [
                    {
                        image: {
                            width: 512,
                            height: 513,
                            url: 'url',
                        },
                    },
                ],
            }
            const comment = await underTest.updateComment('comment-id', attributes)

            // then
            expect(comment).toEqual(expectedCommentModel)
            expect(scope.isDone()).toBeTruthy()
        })

        test('should throw when asset more than 1 defined assets', async () => {
            // given
            const underTest = new PostAPI(Environment.DEVEL)
            const attributes = {
                text: 'text',
                textHtml: 'text-html',
                publishedAt: '2024-02-23T13:55:30.311615168Z',
                assets: [
                    {
                        image: {
                            width: 512,
                            height: 513,
                            url: 'url',
                        },
                        document: {
                            type: PostDocumentType.DOCX,
                            name: 'document-name',
                            url: 'url',
                        },
                    },
                ],
            }

            await expect(async () => {
                await underTest.createComment('parent-id', attributes)
            }).rejects.toBeInstanceOf(GraphQLError)
        })

        test('should throw when asset has no defined assets', async () => {
            // given
            const underTest = new PostAPI(Environment.DEVEL)
            const attributes = {
                text: 'text',
                textHtml: 'text-html',
                publishedAt: '2024-02-23T13:55:30.311615168Z',
                assets: [{}],
            }

            await expect(async () => {
                await underTest.createComment('parent-id', attributes)
            }).rejects.toBeInstanceOf(GraphQLError)
        })
    })

    describe('method: deleteComment', () => {
        test('should call api to delete given comment', async () => {
            const underTest = new PostAPI(Environment.DEVEL)
            const scope = nock('https://devel-api-90568653510.europe-west1.run.app')
                .delete(`/v1/comments/comment-id`)
                .reply(200)

            await underTest.deleteComment('comment-id')

            expect(scope.isDone()).toBeTruthy()
        })
    })

    describe('method: deletePost', () => {
        test('should call api to delete given post', async () => {
            const underTest = new PostAPI(Environment.DEVEL)
            const scope = nock('https://devel-api-90568653510.europe-west1.run.app')
                .delete(`/v4/posts/post-id`)
                .reply(200)

            await underTest.deletePost('post-id')

            expect(scope.isDone()).toBeTruthy()
        })
    })

    describe('method: createPost', () => {
        test('should call api to create a post', async () => {
            // given
            const underTest = new PostAPI(Environment.DEVEL)
            const scope = nock('https://devel-api-90568653510.europe-west1.run.app')
                .post(`/v4/posts`, (body) => {
                    return (
                        JSON.stringify(body) ===
                        JSON.stringify({
                            attributes: {
                                assets: [
                                    {
                                        image: {
                                            width: 512,
                                            height: 513,
                                            url: 'url',
                                            hidden: false,
                                        },
                                    },
                                    {
                                        gjirafa: {
                                            id: 'vjsnrkvt',
                                        },
                                        thumbnail: 'https://thumbnail.com/1683819633.png',
                                    },
                                    {
                                        document: {
                                            name: 'document.gp5',
                                            url: 'https://documents.com/document.gp5',
                                            type: DocumentType.GP5,
                                        },
                                        thumbnail: 'https://thumbnail.com/document.gp5',
                                    },
                                    {
                                        gjirafaLivestream: {
                                            id: 'bwafdasf',
                                        },
                                    },
                                ],
                                text: 'text',
                                textHtml: 'text-html',
                                textDelta: 'text-delta',
                            },
                            isAgeRestricted: true,
                            isSponsored: true,
                            categories: ['sports'],
                            communityId: '0f9b4687-31fe-4a98-995e-45edfa1e6fcd',
                            hasPreview: false,
                            publishedAt: '2024-02-13T13:34:04Z',
                        })
                    )
                })
                .reply(200, postResponse())

            // when
            const attributes = {
                text: 'text',
                textHtml: 'text-html',
                textDelta: 'text-delta',
                categories: ['sports'],
                publishedAt: '2024-02-13T13:34:04Z',
                isSponsored: true,
                isAgeRestricted: true,
                communityId: '0f9b4687-31fe-4a98-995e-45edfa1e6fcd',
                assets: [
                    {
                        image: {
                            width: 512,
                            height: 513,
                            url: 'url',
                        },
                    },
                    {
                        gjirafa: {
                            id: 'vjsnrkvt',
                            thumbnailUrl: 'https://thumbnail.com/1683819633.png',
                        },
                    },
                    {
                        document: {
                            type: PostDocumentType.GP5,
                            name: 'document.gp5',
                            url: 'https://documents.com/document.gp5',
                            thumbnailUrl: 'https://thumbnail.com/document.gp5',
                        },
                    },
                    {
                        gjirafaLivestream: {
                            id: 'bwafdasf',
                        },
                    },
                ],
                hasPreview: false,
            }

            const post = await underTest.createPost(attributes)

            // then
            const expectedPost = {
                ...expectedPostModel,
            }

            delete expectedPost.savedPostInfo

            expect(scope.isDone()).toBeTruthy()
            expect(post).toEqual(expectedPost)
        })
    })

    describe('method: updatePost', () => {
        test('should call api to update a post', async () => {
            // given
            const underTest = new PostAPI(Environment.DEVEL)
            const scope = nock('https://devel-api-90568653510.europe-west1.run.app')
                .put(`/v4/posts/post-id`, (body) => {
                    return (
                        JSON.stringify(body) ===
                        JSON.stringify({
                            attributes: {
                                assets: [
                                    {
                                        image: {
                                            width: 512,
                                            height: 513,
                                            url: 'url',
                                            hidden: false,
                                        },
                                    },
                                    {
                                        gjirafa: {
                                            id: 'vjsnrkvt',
                                        },
                                        thumbnail: 'https://thumbnail.com/1683819633.png',
                                    },
                                    {
                                        document: {
                                            name: 'document.gp5',
                                            url: 'https://documents.com/document.gp5',
                                            type: DocumentType.GP5,
                                        },
                                        thumbnail: 'https://thumbnail.com/document.gp5',
                                    },
                                    {
                                        gjirafaLivestream: {
                                            id: 'bwafdasf',
                                        },
                                    },
                                ],
                                text: 'text',
                                textHtml: 'text-html',
                                textDelta: 'text-delta',
                            },
                            isAgeRestricted: true,
                            isSponsored: true,
                            categories: ['sports'],
                            hasPreview: false,
                            publishedAt: '2024-02-13T13:34:04Z',
                            pinnedAt: '2024-02-14T13:34:04Z',
                            excludeFromRss: false,
                        })
                    )
                })
                .reply(200, postResponse())

            // when
            const attributes = {
                title: undefined,
                text: 'text',
                textHtml: 'text-html',
                textDelta: 'text-delta',
                categories: ['sports'],
                publishedAt: '2024-02-13T13:34:04Z',
                isAgeRestricted: true,
                isSponsored: true,
                isExcludedFromRss: false,
                pinnedAt: '2024-02-14T13:34:04Z',
                hasPreview: false,
                assets: [
                    {
                        image: {
                            width: 512,
                            height: 513,
                            url: 'url',
                        },
                    },
                    {
                        gjirafa: {
                            id: 'vjsnrkvt',
                            thumbnailUrl: 'https://thumbnail.com/1683819633.png',
                        },
                    },
                    {
                        document: {
                            type: PostDocumentType.GP5,
                            name: 'document.gp5',
                            url: 'https://documents.com/document.gp5',
                            thumbnailUrl: 'https://thumbnail.com/document.gp5',
                        },
                    },
                    {
                        gjirafaLivestream: {
                            id: 'bwafdasf',
                        },
                    },
                ],
            }

            const post = await underTest.updatePost('post-id', attributes)

            // then
            const expectedPost = {
                ...expectedPostModel,
            }

            delete expectedPost.savedPostInfo

            expect(scope.isDone()).toBeTruthy()
            expect(post).toEqual(expectedPost)
        })
    })

    describe('method: getComment', () => {
        test('should make a call to get reply comment and correctly map it', async () => {
            // given
            const underTest = new PostAPI(Environment.DEVEL)
            const commentId = 'comment-id'
            const scope = nock('https://devel-api-90568653510.europe-west1.run.app')
                .get(`/v1/comments/${commentId}`)
                .reply(200, replyToReplyResponse())

            // when
            const comment = await underTest.getComment(commentId)

            expect(comment).toEqual<CommentModel>({
                id: 'hunghoangzfgvmdemdlfutxjpvxtuvqsqeda-vbzfkdzk-fiyyqucl',
                userId: 'jonasuroingpj',
                parentId: 'hunghoangzfgvmdemdlfutxjpvxtuvqsqeda-vbzfkdzk',
                siblingId: 'hunghoangzfgvmdemdlfutxjpvxtuvqsqeda-vbzfkdzk-fsqcejsq',
                state: PostState.PUBLISHED,
                text: 'aaa',
                textHtml: 'aaa',
                textDelta: '{"ops":[{"insert":"aaa\\n"}]}',
                assets: [],
                counts: {
                    comments: 0,
                    replies: 0,
                },
                publishedAt: '2024-02-13T13:34:04.833970Z',
                parent: {
                    id: 'hunghoangzfgvmdemdlfutxjpvxtuvqsqeda-vbzfkdzk',
                    userId: 'jonasuroingpj',
                    parentId: 'hunghoangzfgvmdemdlfutxjpvxtuvqsqeda',
                    state: PostState.PUBLISHED,
                    text: 'asdf',
                    textHtml: 'asdf',
                    textDelta: '{"ops":[{"insert":"asdf\\n"}]}',
                    assets: [],
                    counts: {
                        comments: 4,
                        replies: 1,
                    },
                    publishedAt: '2024-02-13T12:11:42.384575Z',
                    myVote: 0,
                    type: 'comment',
                    voteScore: 0,
                },
                post: {
                    id: 'hunghoangzfgvmdemdlfutxjpvxtuvqsqeda',
                    userId: 'hunghoangzfgvmdem',
                    state: PostState.PUBLISHED,
                    text: 'scary pejsek',
                    textHtml: '<p>scary pejsek</p>',
                    textDelta: '{"ops":[{"insert":"scary pejsek\\n"}]}',
                    fullAssets: true,
                    pinnedAt: '2023-11-22T12:17:41.667Z',
                    previewAssets: [],
                    assets: [
                        {
                            url: 'https://assets.herohero.co/devel/images/post/hunghoangzfgvmdem/1691579645-42132228-1515.webp',
                            width: 971,
                            height: 728,
                            assetType: 'image',
                        },
                    ],
                    categories: [],
                    counts: {
                        comments: 3,
                        replies: 18,
                    },
                    publishedAt: '2023-08-09T11:25:00Z',
                    isSponsored: true,
                    isAgeRestricted: false,
                    isExcludedFromRss: false,
                    voteScore: 0,
                    myVote: 0,
                    hasPreview: false,
                    hasPreviewInternal: false,
                },
                voteScore: 0,
                myVote: 0,
            })

            expect(scope.isDone()).toBeTruthy()
        })

        test('should make a call to get post comment and correctly map it', async () => {
            // given
            const underTest = new PostAPI(Environment.DEVEL)
            const commentId = 'comment-id'
            const scope = nock('https://devel-api-90568653510.europe-west1.run.app')
                .get(`/v1/comments/${commentId}`)
                .reply(200, newCommentResponse())

            // when
            const comment = await underTest.getComment(commentId)

            expect(comment).toEqual<CommentModel>({
                id: 'hunghoangzfgvmdemdlfutxjpvxtuvqsqeda-vbzfkdzk',
                state: PostState.PUBLISHED,
                userId: 'jonasuroingpj',
                parentId: 'hunghoangzfgvmdemdlfutxjpvxtuvqsqeda',
                text: 'asdf',
                textHtml: 'asdf',
                textDelta: '{"ops":[{"insert":"asdf\\n"}]}',
                assets: [],
                counts: {
                    comments: 4,
                    replies: 1,
                },
                publishedAt: '2024-02-13T12:11:42.384575Z',
                parent: {
                    id: 'hunghoangzfgvmdemdlfutxjpvxtuvqsqeda',
                    state: PostState.PUBLISHED,
                    text: 'scary pejsek',
                    textHtml: '<p>scary pejsek</p>',
                    textDelta: '{"ops":[{"insert":"scary pejsek\\n"}]}',
                    fullAssets: true,
                    pinnedAt: '2023-11-22T12:17:41.667Z',
                    previewAssets: [],
                    assets: [
                        {
                            url: 'https://assets.herohero.co/devel/images/post/hunghoangzfgvmdem/1691579645-42132228-1515.webp',
                            width: 971,
                            height: 728,
                            assetType: 'image',
                        },
                    ],
                    categories: [],
                    counts: {
                        comments: 2,
                        replies: 4,
                    },
                    publishedAt: '2023-08-09T11:25:00Z',
                    userId: 'hunghoangzfgvmdem',
                    isSponsored: true,
                    isAgeRestricted: false,
                    type: 'post',
                    isExcludedFromRss: false,
                    myVote: 0,
                    voteScore: 0,
                    hasPreview: false,
                    hasPreviewInternal: false,
                },
                post: {
                    id: 'hunghoangzfgvmdemdlfutxjpvxtuvqsqeda',
                    state: PostState.PUBLISHED,
                    text: 'scary pejsek',
                    textHtml: '<p>scary pejsek</p>',
                    textDelta: '{"ops":[{"insert":"scary pejsek\\n"}]}',
                    fullAssets: true,
                    pinnedAt: '2023-11-22T12:17:41.667Z',
                    previewAssets: [],
                    assets: [
                        {
                            url: 'https://assets.herohero.co/devel/images/post/hunghoangzfgvmdem/1691579645-42132228-1515.webp',
                            width: 971,
                            height: 728,
                            assetType: 'image',
                        },
                    ],
                    categories: [],
                    counts: {
                        comments: 2,
                        replies: 4,
                    },
                    publishedAt: '2023-08-09T11:25:00Z',
                    isSponsored: true,
                    isAgeRestricted: false,
                    userId: 'hunghoangzfgvmdem',
                    isExcludedFromRss: false,
                    myVote: 0,
                    voteScore: 0,
                    hasPreview: false,
                    hasPreviewInternal: false,
                },
                myVote: 0,
                voteScore: 0,
            })

            expect(scope.isDone()).toBeTruthy()
        })
    })
})

const expectedCommentModel: CommentModel = {
    id: 'post-id',
    userId: 'user-id',
    parentId: 'parent-id',
    text: 'post-text',
    textHtml: '<h1>Header</h1>',
    textDelta: '{}',
    publishedAt: '2023-09-17T00:00:00Z',
    counts: {
        comments: 15,
        replies: 20,
    },
    state: PostModelState.DELETED,
    assets: [
        {
            url: 'image-url',
            width: 700,
            height: 600,
            assetType: 'image',
        },
        {
            name: 'document-name',
            url: 'document-url',
            type: PostDocumentType.DOCX,
            assetType: 'document',
        },
        {
            assetType: 'gjirafa',
            hidden: false,
            hasAudio: true,
            hasVideo: true,
            key: 'sfyMS3YYBbCjjZhQG4D7Ig==,7991601123',
            keyId: '20FFB244-27C5-0C40-A00B-D28096FF7216',
            id: 'id',
            duration: 45.1,
            height: 0,
            width: 0,
            progressTillCompleteness: 0,
            progressTillReadiness: 0,
            status: GjirafaQualityTypeStatus.COMPLETE,
            previewStaticUrl:
                'https://cdn.vpplayer.tech/agmipobm/tNTT3UXuKWZTxrJpT36D0w==,4837254998/images/vjsnqosr/original-thumbnail.webp',
            previewAnimatedUrl:
                'https://cdn.vpplayer.tech/agmipobm/tNTT3UXuKWZTxrJpT36D0w==,4837254998/images/vjsnqosr/original-animated.webp',
            previewStripUrl:
                'https://cdn.vpplayer.tech/agmipobm/epDKgAZEDoALFiwtjVcmIg==,7991601123/encode/vjsnxsqt/thumbnails/filmstrip.vtt',
            thumbnailUrl: 'thumbnail',
            thumbnail: {
                url: 'thumbnail',
                width: 700,
                height: 600,
            },
            isLivestreamRecording: false,
            timestamp: 420.0,
        },
        {
            id: 'vdjnejr',
            status: GjirafaLivestreamStatus.LIVE,
            channelPublicId: 'adqmnej',
            playbackUrl: 'playback-url',
            assetType: 'gjirafa-livestream',
            thumbnailUrl: 'thumbnail',
            thumbnail: {
                url: 'thumbnail',
                width: 700,
                height: 600,
            },
        },
        {
            id: 'youtube-id',
            thumbnailUrl: 'thumbnail-youtube',
            assetType: 'youtube',
            thumbnail: {
                url: 'thumbnail',
                width: 700,
                height: 600,
            },
        },
        {
            assetType: 'empty',
            dummy: 'empty',
        },
    ],
    myVote: 0,
    voteScore: 0,
}

const expectedPostModel: PostModel = {
    id: 'post-id',
    userId: 'user-id',
    title: 'title',
    text: 'post-text',
    textHtml: '<h1>Header</h1>',
    textDelta: '{}',
    publishedAt: '2023-09-17T00:00:00Z',
    pinnedAt: '2023-09-18T00:00:00Z',
    counts: {
        comments: 15,
        replies: 20,
    },
    state: PostModelState.DELETED,
    price: 10,
    fullAssets: false,
    isExcludedFromRss: false,
    previewAssets: [
        {
            assetType: 'preview-image',
            height: 50,
            url: 'http://localhost:8080',
            width: 100,
        },
        {
            assetType: 'preview-gjirafa',
            previewAnimatedUrl:
                'https://cdn.vpplayer.tech/agmipobm/tNTT3UXuKWZTxrJpT36D0w==,4837254998/images/vjsnqosr/original-animated.webp',
            previewStaticUrl:
                'https://cdn.vpplayer.tech/agmipobm/tNTT3UXuKWZTxrJpT36D0w==,4837254998/images/vjsnqosr/original-thumbnail.webp',
            previewStripUrl:
                'https://cdn.vpplayer.tech/agmipobm/epDKgAZEDoALFiwtjVcmIg==,7991601123/encode/vjsnxsqt/thumbnails/filmstrip.vtt',
            duration: 54.1,
            width: 100,
            height: 50,
            thumbnailUrl: 'thumbnail-url',
            type: PostPreviewGjirafaType.VIDEO,
        },
        {
            assetType: 'preview-gjirafa-live',
            thumbnailUrl: 'thumbnail-url-gjirafa-live',
        },
        {
            assetType: 'preview-document',
            type: PostDocumentType.DOCX,
            name: 'document-name',
            thumbnailUrl: 'thumbnail-url',
        },
    ],
    assets: [
        {
            url: 'image-url',
            width: 700,
            height: 600,
            assetType: 'image',
        },
        {
            name: 'document-name',
            url: 'document-url',
            type: PostDocumentType.DOCX,
            assetType: 'document',
        },
        {
            assetType: 'gjirafa',
            hidden: false,
            hasAudio: true,
            hasVideo: true,
            key: 'sfyMS3YYBbCjjZhQG4D7Ig==,7991601123',
            keyId: '20FFB244-27C5-0C40-A00B-D28096FF7216',
            id: 'id',
            duration: 45.1,
            progressTillCompleteness: 0,
            progressTillReadiness: 0,
            width: 0,
            height: 0,
            status: GjirafaQualityTypeStatus.COMPLETE,
            previewStaticUrl:
                'https://cdn.vpplayer.tech/agmipobm/tNTT3UXuKWZTxrJpT36D0w==,4837254998/images/vjsnqosr/original-thumbnail.webp',
            previewAnimatedUrl:
                'https://cdn.vpplayer.tech/agmipobm/tNTT3UXuKWZTxrJpT36D0w==,4837254998/images/vjsnqosr/original-animated.webp',
            previewStripUrl:
                'https://cdn.vpplayer.tech/agmipobm/epDKgAZEDoALFiwtjVcmIg==,7991601123/encode/vjsnxsqt/thumbnails/filmstrip.vtt',
            thumbnailUrl: 'thumbnail',
            thumbnail: {
                url: 'thumbnail',
                width: 700,
                height: 600,
            },
            isLivestreamRecording: false,
            timestamp: 420.0,
        },
        {
            id: 'vdjnejr',
            status: GjirafaLivestreamStatus.LIVE,
            channelPublicId: 'adqmnej',
            playbackUrl: 'playback-url',
            assetType: 'gjirafa-livestream',
            thumbnailUrl: 'thumbnail',
            thumbnail: {
                url: 'thumbnail',
                width: 700,
                height: 600,
            },
        },
        {
            id: 'youtube-id',
            thumbnailUrl: 'thumbnail-youtube',
            assetType: 'youtube',
            thumbnail: {
                url: 'thumbnail',
                width: 700,
                height: 600,
            },
        },
        {
            assetType: 'empty',
            dummy: 'empty',
        },
    ],
    savedPostInfo: {
        id: 'saved-post-id',
        savedAt: '2023-09-18T00:00:00Z',
    },
    categories: [
        {
            id: 'category-id',
            name: 'category-name',
            slug: 'category-slug',
        },
    ],
    isSponsored: true,
    isAgeRestricted: false,
    voteScore: 0,
    myVote: 0,
    hasPreview: false,
    hasPreviewInternal: true,
}

const expectedPollModel: PollModel = {
    id: 'poll-id',
    deadline: '2024-12-31T23:59:59Z',
    options: [
        {
            id: 'option-1',
            title: 'Option 1',
            voteCount: 10,
            hasVotedFor: false,
        },
        {
            id: 'option-2',
            title: 'Option 2',
            voteCount: 5,
            hasVotedFor: true,
        },
    ],
}

function replyToReplyResponse(): CommentResponse {
    return {
        comment: {
            id: 'hunghoangzfgvmdemdlfutxjpvxtuvqsqeda-vbzfkdzk-fiyyqucl',
            state: RestPostState.PUBLISHED,
            text: 'aaa',
            textHtml: 'aaa',
            textDelta: '{"ops":[{"insert":"aaa\\n"}]}',
            fullAsset: true,
            excludeFromRss: false,
            assets: [],
            previewAssets: [],
            categories: [],
            counts: {
                comments: 0,
                replies: 0,
            },
            chapters: [],
            publishedAt: '2024-02-13T13:34:04.833970Z',
            price: 0,
            assetsCount: 0,
            isSponsored: true,
            isAgeRestricted: false,
            relationships: {
                userId: 'jonasuroingpj',
                parentId: 'hunghoangzfgvmdemdlfutxjpvxtuvqsqeda-vbzfkdzk',
                siblingId: 'hunghoangzfgvmdemdlfutxjpvxtuvqsqeda-vbzfkdzk-fsqcejsq',
            },
            myVote: 0,
            voteScore: 0,
            hasPreview: false,
            hasPreviewInternal: false,
        },
        parent: {
            id: 'hunghoangzfgvmdemdlfutxjpvxtuvqsqeda-vbzfkdzk',
            state: RestPostState.PUBLISHED,
            text: 'asdf',
            textHtml: 'asdf',
            textDelta: '{"ops":[{"insert":"asdf\\n"}]}',
            fullAsset: true,
            excludeFromRss: false,
            assets: [],
            previewAssets: [],
            categories: [],
            counts: {
                comments: 4,
                replies: 1,
            },
            chapters: [],
            publishedAt: '2024-02-13T12:11:42.384575Z',
            price: 0,
            assetsCount: 0,
            isSponsored: true,
            isAgeRestricted: false,
            relationships: {
                userId: 'jonasuroingpj',
                parentId: 'hunghoangzfgvmdemdlfutxjpvxtuvqsqeda',
            },
            voteScore: 0,
            myVote: 0,
            hasPreview: false,
            hasPreviewInternal: false,
        },
        rootParent: {
            id: 'hunghoangzfgvmdemdlfutxjpvxtuvqsqeda',
            state: RestPostState.PUBLISHED,
            text: 'scary pejsek',
            textHtml: '<p>scary pejsek</p>',
            textDelta: '{"ops":[{"insert":"scary pejsek\\n"}]}',
            fullAsset: true,
            excludeFromRss: false,
            pinnedAt: '2023-11-22T12:17:41.667Z',
            isSponsored: true,
            isAgeRestricted: false,
            assets: [
                {
                    image: {
                        url: 'https://assets.herohero.co/devel/images/post/hunghoangzfgvmdem/1691579645-42132228-1515.webp',
                        width: 971,
                        height: 728,
                    },
                },
            ],
            previewAssets: [],
            categories: [],
            counts: {
                comments: 3,
                replies: 18,
            },
            chapters: [],
            publishedAt: '2023-08-09T11:25:00Z',
            assetsCount: 1,
            relationships: {
                userId: 'hunghoangzfgvmdem',
            },
            voteScore: 0,
            myVote: 0,
            hasPreview: false,
            hasPreviewInternal: false,
        },
    }
}

function newCommentResponse(): CommentResponse {
    return {
        comment: {
            id: 'hunghoangzfgvmdemdlfutxjpvxtuvqsqeda-vbzfkdzk',
            state: RestPostState.PUBLISHED,
            text: 'asdf',
            textHtml: 'asdf',
            textDelta: '{"ops":[{"insert":"asdf\\n"}]}',
            fullAsset: true,
            excludeFromRss: false,
            assets: [],
            previewAssets: [],
            categories: [],
            counts: {
                comments: 4,
                replies: 1,
            },
            chapters: [],
            publishedAt: '2024-02-13T12:11:42.384575Z',
            price: 0,
            assetsCount: 0,
            isSponsored: true,
            isAgeRestricted: false,
            relationships: {
                userId: 'jonasuroingpj',
                parentId: 'hunghoangzfgvmdemdlfutxjpvxtuvqsqeda',
            },
            voteScore: 0,
            myVote: 0,
            hasPreview: false,
            hasPreviewInternal: false,
        },
        parent: {
            id: 'hunghoangzfgvmdemdlfutxjpvxtuvqsqeda',
            state: RestPostState.PUBLISHED,
            text: 'scary pejsek',
            textHtml: '<p>scary pejsek</p>',
            textDelta: '{"ops":[{"insert":"scary pejsek\\n"}]}',
            fullAsset: true,
            excludeFromRss: false,
            pinnedAt: '2023-11-22T12:17:41.667Z',
            assets: [
                {
                    image: {
                        url: 'https://assets.herohero.co/devel/images/post/hunghoangzfgvmdem/1691579645-42132228-1515.webp',
                        width: 971,
                        height: 728,
                    },
                },
            ],
            previewAssets: [],
            categories: [],
            counts: {
                comments: 2,
                replies: 4,
            },
            chapters: [],
            publishedAt: '2023-08-09T11:25:00Z',
            assetsCount: 1,
            isSponsored: true,
            isAgeRestricted: false,
            relationships: {
                userId: 'hunghoangzfgvmdem',
            },
            voteScore: 0,
            myVote: 0,
            hasPreview: false,
            hasPreviewInternal: false,
        },
        rootParent: {
            id: 'hunghoangzfgvmdemdlfutxjpvxtuvqsqeda',
            state: RestPostState.PUBLISHED,
            text: 'scary pejsek',
            textHtml: '<p>scary pejsek</p>',
            textDelta: '{"ops":[{"insert":"scary pejsek\\n"}]}',
            fullAsset: true,
            excludeFromRss: false,
            pinnedAt: '2023-11-22T12:17:41.667Z',
            assets: [
                {
                    image: {
                        url: 'https://assets.herohero.co/devel/images/post/hunghoangzfgvmdem/1691579645-42132228-1515.webp',
                        width: 971,
                        height: 728,
                    },
                },
            ],
            previewAssets: [],
            categories: [],
            counts: {
                comments: 2,
                replies: 4,
            },
            chapters: [],
            publishedAt: '2023-08-09T11:25:00Z',
            assetsCount: 1,
            isSponsored: true,
            isAgeRestricted: false,
            relationships: {
                userId: 'hunghoangzfgvmdem',
            },
            voteScore: 0,
            myVote: 0,
            hasPreview: false,
            hasPreviewInternal: false,
        },
    }
}

const postDto: PostDto = {
    id: 'post-id',
    attributes: {
        state: RestPostState.DELETED,
        text: 'post-text',
        textHtml: '<h1>Header</h1>',
        textDelta: '{}',
        publishedAt: '2023-09-17T00:00:00Z',
        pinnedAt: '2023-09-18T00:00:00Z',
        assets: [],
        fullAsset: false,
        counts: {
            comments: 15,
            replies: 20,
        },
        chapters: [],
        price: 10,
        excludeFromRss: false,
        isSponsored: true,
        isAgeRestricted: false,
        hasPreview: false,
    },
    relationships: {
        user: {
            id: 'user-id',
            type: 'user',
        },
        paymentsByUsers: [],
        categories: [],
    },
    type: '',
}
