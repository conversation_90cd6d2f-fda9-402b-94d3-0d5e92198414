import nock from 'nock'
import { Environment } from '../../src/common/environment'
import { PostModel, SavedPostModel } from '../../src/models/post'
import {
    GjirafaLivestreamStatus,
    GjirafaQualityTypeStatus,
    PostDocumentType,
    PostPreviewGjirafaType,
    PostState as PostModelState,
} from '../../src/generated/resolvers-types'
import { PagedSavedPostResponse, SavedPostResponse } from '../../src/generated/api'
import { LibraryAPI } from '../../src/datasources/LibraryAPI'
import { postResponse } from './test-utils'
import { PaginationModel } from '../../src/models/pagination'

beforeEach(() => {
    nock.disableNetConnect()
})

describe('data source: LibraryAPI', () => {
    describe('method: getSavedPosts', () => {
        test('should make a call to get saved posts and correctly map them', async () => {
            // given
            const underTest = new LibraryAPI(Environment.DEVEL)
            const scope = nock('https://devel-api-90568653510.europe-west1.run.app')
                .get(`/v1/library`)
                .query({ pageSize: 10, afterCursor: 'afterCursor', subscribedCreatorsOnly: 'true' })
                .reply(200, getSavedPostsResponse())

            // when
            const { savedPosts, pagination } = await underTest.getSavedPosts({ first: 10, after: 'afterCursor' }, true)

            // then
            expect(savedPosts).toEqual<SavedPostModel[]>([
                {
                    id: 'saved-post-id',
                    savedAt: '2023-05-10T12:12:11.844280Z',
                    post: expectedPostModel,
                },
            ])
            expect(pagination).toEqual<PaginationModel>({
                hasNextPage: false,
                endCursor: 'eyJ1c2VySWQiOiJodW5naG9hbmd6Zmd2bWRlbSIsImxh',
            })
            expect(scope.isDone()).toBeTruthy()
        })
    })

    describe('method: addPostToLibrary', () => {
        test('should make a call to add post to the library', async () => {
            // given
            const underTest = new LibraryAPI(Environment.DEVEL)
            const scope = nock('https://devel-api-90568653510.europe-west1.run.app')
                .post(`/v1/library`)
                .reply(200, savedPostResponse())

            // when
            const savedPost = await underTest.addPostToLibrary('post-id')

            // then
            expect(savedPost).toEqual<SavedPostModel>({
                id: 'saved-post-id',
                savedAt: '2023-05-10T12:12:11.844280Z',
                post: expectedPostModel,
            })
            expect(scope.isDone()).toBeTruthy()
        })
    })

    describe('method: removePostFromLibrary', () => {
        test('should make a call to remove post from the library', async () => {
            // given
            const underTest = new LibraryAPI(Environment.DEVEL)
            const scope = nock('https://devel-api-90568653510.europe-west1.run.app')
                .delete(`/v1/library/saved-post-id`)
                .reply(204)

            // when
            await underTest.removePostFromLibrary('saved-post-id')

            expect(scope.isDone()).toBeTruthy()
        })
    })
})

function savedPostResponse(): SavedPostResponse {
    return {
        id: 'saved-post-id',
        savedAt: '2023-05-10T12:12:11.844280Z',
        post: postResponse(),
    }
}

function getSavedPostsResponse(): PagedSavedPostResponse {
    return {
        content: [savedPostResponse()],
        hasNext: false,
        afterCursor: 'eyJ1c2VySWQiOiJodW5naG9hbmd6Zmd2bWRlbSIsImxh',
    }
}

const expectedPostModel: PostModel = {
    id: 'post-id',
    userId: 'user-id',
    title: 'title',
    text: 'post-text',
    textHtml: '<h1>Header</h1>',
    textDelta: '{}',
    publishedAt: '2023-09-17T00:00:00Z',
    pinnedAt: '2023-09-18T00:00:00Z',
    counts: {
        comments: 15,
        replies: 20,
    },
    state: PostModelState.DELETED,
    price: 10,
    fullAssets: false,
    isSponsored: true,
    isAgeRestricted: false,
    isExcludedFromRss: false,
    previewAssets: [
        {
            assetType: 'preview-image',
            height: 50,
            url: 'http://localhost:8080',
            width: 100,
        },
        {
            assetType: 'preview-gjirafa',
            previewAnimatedUrl:
                'https://cdn.vpplayer.tech/agmipobm/tNTT3UXuKWZTxrJpT36D0w==,4837254998/images/vjsnqosr/original-animated.webp',
            previewStaticUrl:
                'https://cdn.vpplayer.tech/agmipobm/tNTT3UXuKWZTxrJpT36D0w==,4837254998/images/vjsnqosr/original-thumbnail.webp',
            previewStripUrl:
                'https://cdn.vpplayer.tech/agmipobm/epDKgAZEDoALFiwtjVcmIg==,7991601123/encode/vjsnxsqt/thumbnails/filmstrip.vtt',
            duration: 54.1,
            width: 100,
            height: 50,
            type: PostPreviewGjirafaType.VIDEO,
            thumbnailUrl: 'thumbnail-url',
        },
        {
            assetType: 'preview-gjirafa-live',
            thumbnailUrl: 'thumbnail-url-gjirafa-live',
        },
        {
            assetType: 'preview-document',
            type: PostDocumentType.DOCX,
            name: 'document-name',
            thumbnailUrl: 'thumbnail-url',
        },
    ],
    assets: [
        {
            url: 'image-url',
            width: 700,
            height: 600,
            assetType: 'image',
        },
        {
            name: 'document-name',
            url: 'document-url',
            type: PostDocumentType.DOCX,
            assetType: 'document',
        },
        {
            assetType: 'gjirafa',
            hidden: false,
            hasAudio: true,
            hasVideo: true,
            key: 'sfyMS3YYBbCjjZhQG4D7Ig==,7991601123',
            keyId: '20FFB244-27C5-0C40-A00B-D28096FF7216',
            id: 'id',
            duration: 45.1,
            width: 0,
            height: 0,
            progressTillCompleteness: 0,
            progressTillReadiness: 0,
            status: GjirafaQualityTypeStatus.COMPLETE,
            previewStaticUrl:
                'https://cdn.vpplayer.tech/agmipobm/tNTT3UXuKWZTxrJpT36D0w==,4837254998/images/vjsnqosr/original-thumbnail.webp',
            previewAnimatedUrl:
                'https://cdn.vpplayer.tech/agmipobm/tNTT3UXuKWZTxrJpT36D0w==,4837254998/images/vjsnqosr/original-animated.webp',
            previewStripUrl:
                'https://cdn.vpplayer.tech/agmipobm/epDKgAZEDoALFiwtjVcmIg==,7991601123/encode/vjsnxsqt/thumbnails/filmstrip.vtt',
            thumbnailUrl: 'thumbnail',
            thumbnail: {
                url: 'thumbnail',
                width: 700,
                height: 600,
            },
            isLivestreamRecording: false,
            timestamp: 420.0,
        },
        {
            id: 'vdjnejr',
            status: GjirafaLivestreamStatus.LIVE,
            channelPublicId: 'adqmnej',
            playbackUrl: 'playback-url',
            assetType: 'gjirafa-livestream',
            thumbnailUrl: 'thumbnail',
            thumbnail: {
                url: 'thumbnail',
                width: 700,
                height: 600,
            },
        },
        {
            id: 'youtube-id',
            thumbnailUrl: 'thumbnail-youtube',
            assetType: 'youtube',
            thumbnail: {
                url: 'thumbnail',
                width: 700,
                height: 600,
            },
        },
        {
            assetType: 'empty',
            dummy: 'empty',
        },
    ],
    categories: [
        {
            id: 'category-id',
            name: 'category-name',
            slug: 'category-slug',
        },
    ],
    voteScore: 0,
    myVote: 0,
    hasPreview: false,
    hasPreviewInternal: true,
}
