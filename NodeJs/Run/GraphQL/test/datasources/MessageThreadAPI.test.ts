import nock from 'nock'
import { Environment } from '../../src/common/environment'
import {
    DocumentType,
    MessageThreadDetailsResponse,
    MessageThreadResponse,
    PagedMessageThreadsResponse,
    PostAssetInput,
} from '../../src/generated/api'
import { MessageThreadAPI } from '../../src/datasources/MessageThreadAPI'
import { PaginationModel } from '../../src/models/pagination'
import { MessageModel, MessageThreadModel } from '../../src/models/message-thread'
import {
    Currency,
    GjirafaLivestreamStatus,
    GjirafaQualityTypeStatus,
    PostDocumentType,
    SubscriptionRelationType,
    UserProfileType,
} from '../../src/generated/resolvers-types'
import { pagedPostResponse, postResponse, userResponse } from './test-utils'

beforeEach(() => {
    nock.disableNetConnect()
})

describe('data source: MessageThreadAPI', () => {
    describe('method: getMessageThreads', () => {
        test('should make a call to get a users message threads and correctly map it', async () => {
            // given
            const userId = 'user-id'
            const underTest = new MessageThreadAPI(Environment.DEVEL, userId)
            const scope = nock('https://devel-api-90568653510.europe-west1.run.app')
                .get(`/v2/message-threads`)
                .query({ pageSize: 15, afterCursor: 'after-cursor' })
                .reply(200, pagedMessageThreadsResponse())

            // when
            const { messageThreads, pagination } = await underTest.getMessageThreads({
                first: 15,
                after: 'after-cursor',
            })

            expect(messageThreads).toEqual<MessageThreadModel[]>([expectedMessageThread])
            expect(pagination).toEqual<PaginationModel>({
                hasNextPage: false,
                endCursor: 'eyJ1c2VySWQiOiJodW5naG9hbmd6Zmd2bWRlbSIsImxh',
            })
            expect(scope.isDone()).toBeTruthy()
        })
    })

    describe('method: getMessageThread', () => {
        test('should make a call to get a single message thread and correctly map it', async () => {
            // given
            const userId = 'user-id'
            const underTest = new MessageThreadAPI(Environment.DEVEL, userId)
            const scope = nock('https://devel-api-90568653510.europe-west1.run.app')
                .get(`/v2/message-threads/message-thread-id`)
                .reply(200, messageThreadDetailsResponse())

            // when
            const messageThread = await underTest.getMessageThread('message-thread-id')

            // then
            expect(messageThread).toEqual<MessageThreadModel>({
                id: '1683720731840-1319805956',
                participants: [],
                participantIds: ['hunghoangzfgvmdem', 'eliskaslaharovanojvddav'],
                createdAt: '2023-05-10T12:12:11.844280Z',
                seenAt: '2023-10-30T17:10:47.109783Z',
                checkedAt: '2023-11-29T17:10:47.109783Z',
                lastMessageAt: '2023-12-28T17:10:47.109783Z',
                canMessage: true,
            })

            expect(scope.isDone()).toBeTruthy()
        })
    })

    describe('method: getMessages', () => {
        test('should make a call to get messages from a thread and correctly map them', async () => {
            // given
            const userId = 'user-id'
            const underTest = new MessageThreadAPI(Environment.DEVEL, userId)
            const scope = nock('https://devel-api-90568653510.europe-west1.run.app')
                .get(`/v2/message-threads/message-thread-id/messages`)
                .query({ pageSize: 9, afterCursor: 'after-cursor' })
                .reply(200, pagedPostResponse())

            // when
            const { messages, pagination } = await underTest.getMessages('message-thread-id', {
                first: 9,
                after: 'after-cursor',
            })

            // then
            expect(messages).toEqual<MessageModel[]>([expectedMessage])
            expect(pagination).toEqual<PaginationModel>({
                hasNextPage: true,
                endCursor: 'eyJ1c2VySWQiOiJodW5naG9hbmd6Zmd2bWRlbSIsImxhc3RNZXNzYWdlQXQiOiIyMD',
            })
            expect(scope.isDone()).toBeTruthy()
        })
    })

    describe('method: getMessage', () => {
        test('should make a call to get single message and correctly map them', async () => {
            // given
            const userId = 'user-id'
            const messageId = 'message-id'
            const underTest = new MessageThreadAPI(Environment.DEVEL, userId)
            const scope = nock('https://devel-api-90568653510.europe-west1.run.app')
                .get(`/v1/messages/${messageId}`)
                .reply(200, postResponse())

            // when
            const message = await underTest.getMessage(messageId)

            // then
            expect(message).toEqual<MessageModel>(expectedMessage)
            expect(scope.isDone()).toBeTruthy()
        })
    })

    describe('method: postMessages', () => {
        test('should make a call to post a message to a thread and correctly map the response', async () => {
            // given
            const userId = 'user-id'
            const messageThreadId = 'message-thread-id'
            const messageText = 'Hello, this is a test message'
            const underTest = new MessageThreadAPI(Environment.DEVEL, userId)
            const assets: PostAssetInput[] = [
                {
                    image: {
                        url: 'image-url',
                        width: 800,
                        height: 600,
                        hidden: false,
                    },
                },
                {
                    document: {
                        url: 'document-url',
                        type: DocumentType.PDF,
                        name: 'welcome.pdf',
                    },
                },
            ]
            const scope = nock('https://devel-api-90568653510.europe-west1.run.app')
                .post(`/v2/message-threads/${messageThreadId}/messages`, (body) => {
                    expect(body).toMatchObject({
                        priceCents: 100,
                        text: messageText,
                        assets: [
                            {
                                image: {
                                    url: 'image-url',
                                    width: 800,
                                    height: 600,
                                    hidden: false,
                                },
                            },
                            {
                                document: {
                                    url: 'document-url',
                                    type: PostDocumentType.PDF,
                                    name: 'welcome.pdf',
                                },
                            },
                        ],
                    })
                    return true
                })
                .reply(200, postResponse())

            // when
            const message = await underTest.postMessages({
                messageThreadId,
                text: messageText,
                assets,
                priceCents: 100,
            })

            // then
            expect(message).toEqual<MessageModel>(expectedMessage)
            expect(scope.isDone()).toBeTruthy()
        })
    })

    describe('method: postMessageThreads', () => {
        test('should make a call to create a message thread and correctly map the response', async () => {
            // given
            const userId = 'user-id'
            const participantIds = ['participant-1', 'participant-2']
            const underTest = new MessageThreadAPI(Environment.DEVEL, userId)
            const mockResponse = {
                id: 'message-thread-id',
                type: 'message-thread',
                attributes: {
                    commonCreators: [],
                    deleted: false,
                    archived: false,
                    createdAt: '2023-09-17T00:00:00Z',
                    seenAt: '2023-09-18T00:00:00Z',
                    checkedAt: '2023-09-19T00:00:00Z',
                    lastMessageAt: '2023-09-20T00:00:00Z',
                    canPost: true,
                },
                relationships: {
                    users: [
                        { id: userId, type: 'user' },
                        { id: 'participant-1', type: 'user' },
                        { id: 'participant-2', type: 'user' },
                    ],
                },
            }
            const scope = nock('https://devel-api-90568653510.europe-west1.run.app')
                .post(`/v1/message-threads`, {
                    type: 'message-thread',
                    attributes: {
                        commonCreators: [],
                        deleted: false,
                        archived: false,
                    },
                    relationships: {
                        users: [
                            { id: userId, type: 'user' },
                            { id: 'participant-1', type: 'user' },
                            { id: 'participant-2', type: 'user' },
                        ],
                    },
                })
                .reply(200, mockResponse)

            // when
            const messageThread = await underTest.postMessageThreads(userId, participantIds)

            // then
            expect(messageThread).toEqual({
                id: 'message-thread-id',
                participants: [],
                participantIds: ['user-id', 'participant-1', 'participant-2'],
                createdAt: '2023-09-17T00:00:00Z',
                seenAt: '2023-09-18T00:00:00Z',
                checkedAt: '2023-09-19T00:00:00Z',
                lastMessageAt: '2023-09-20T00:00:00Z',
                lastMessage: undefined,
                canMessage: true,
            })
            expect(scope.isDone()).toBeTruthy()
        })
    })

    describe('method: patchMessageThread', () => {
        test('should make a call to patch a message thread with checked=true', async () => {
            // given
            const userId = 'user-id'
            const messageThreadId = 'message-thread-id'
            const underTest = new MessageThreadAPI(Environment.DEVEL, userId)

            const scope = nock('https://devel-api-90568653510.europe-west1.run.app')
                .patch(`/v1/message-threads/${messageThreadId}`, (body) => {
                    // Verify the structure and that checkedAt is a valid ISO string
                    expect(body).toMatchObject({
                        type: 'message-thread',
                        id: messageThreadId,
                        attributes: {
                            commonCreators: [],
                            deleted: false,
                            archived: false,
                            checkedAt: '2023-09-17T00:00:00Z',
                        },
                        relationships: {
                            users: [],
                        },
                    })
                    return true
                })
                .reply(200)

            // when
            await underTest.patchMessageThread(messageThreadId, '2023-09-17T00:00:00Z')

            // then
            expect(scope.isDone()).toBeTruthy()
        })

        test('should make a call to patch a message thread with checked=false', async () => {
            // given
            const userId = 'user-id'
            const messageThreadId = 'message-thread-id'
            const underTest = new MessageThreadAPI(Environment.DEVEL, userId)
            const expectedBody = {
                type: 'message-thread',
                id: messageThreadId,
                attributes: {
                    commonCreators: [],
                    deleted: false,
                    archived: false,
                    checkedAt: undefined,
                },
                relationships: {
                    users: [],
                },
            }

            const scope = nock('https://devel-api-90568653510.europe-west1.run.app')
                .patch(`/v1/message-threads/${messageThreadId}`, expectedBody)
                .reply(200)

            // when
            await underTest.patchMessageThread(messageThreadId, undefined)

            // then
            expect(scope.isDone()).toBeTruthy()
        })
    })

    describe('method: markAllSeen', () => {
        test('should make a call to mark all message threads as seen for a user', async () => {
            // given
            const userId = 'user-id'
            const underTest = new MessageThreadAPI(Environment.DEVEL, userId)
            const scope = nock('https://devel-api-90568653510.europe-west1.run.app')
                .post(`/v1/users/${userId}/mark-message-threads-seen`)
                .reply(200)

            // when
            await underTest.markAllSeen(userId)

            // then
            expect(scope.isDone()).toBeTruthy()
        })
    })

    describe('method: getWelcomeMessage', () => {
        test('should make a call to get welcome message for a user and correctly map it', async () => {
            // given
            const userId = 'user-id'
            const underTest = new MessageThreadAPI(Environment.DEVEL, userId)
            const scope = nock('https://devel-api-90568653510.europe-west1.run.app')
                .get(`/v1/users/${userId}/welcome-messages`)
                .reply(200, postResponse())

            // when
            const message = await underTest.getWelcomeMessage(userId)

            // then
            expect(message).toEqual<MessageModel>(expectedMessage)
            expect(scope.isDone()).toBeTruthy()
        })
    })

    describe('method: updateWelcomeMessage', () => {
        test('should make a call to update welcome message with text and assets', async () => {
            // given
            const userId = 'user-id'
            const text = 'Updated welcome message text'
            const assets: PostAssetInput[] = [
                {
                    image: {
                        url: 'image-url',
                        width: 800,
                        height: 600,
                        hidden: false,
                    },
                },
                {
                    document: {
                        url: 'document-url',
                        type: DocumentType.PDF,
                        name: 'welcome.pdf',
                    },
                },
            ]
            const underTest = new MessageThreadAPI(Environment.DEVEL, userId)

            const scope = nock('https://devel-api-90568653510.europe-west1.run.app')
                .put(`/v1/users/${userId}/welcome-messages`, (body) => {
                    expect(body).toMatchObject({
                        text: 'Updated welcome message text',
                        assets: [
                            {
                                image: {
                                    url: 'image-url',
                                    width: 800,
                                    height: 600,
                                    hidden: false,
                                },
                            },
                            {
                                document: {
                                    url: 'document-url',
                                    type: PostDocumentType.PDF,
                                    name: 'welcome.pdf',
                                },
                            },
                        ],
                    })
                    return true
                })
                .reply(200, postResponse())

            // when
            const message = await underTest.updateWelcomeMessage(userId, text, assets)

            // then
            expect(message).toEqual<MessageModel>(expectedMessage)
            expect(scope.isDone()).toBeTruthy()
        })
    })
})

const expectedMessageThread: MessageThreadModel = {
    id: '1683720731840-1319805956',
    canMessage: false,
    participants: [
        {
            id: 'hunghoangzfgvmdem',
            name: 'user-name',
            bio: 'user-bio',
            image: {
                url: 'image-id',
                width: 420,
                height: 69,
                hidden: true,
            },
            path: 'user-path',
            subscribable: true,
            verified: false,
            spotifyShowUri: 'spotify-uri',
            counts: {
                supporting: 150,
                supporters: 21231,
                supportersThreshold: 25000,
                posts: 243,
                ownedCommunities: 1,
            },
            hasRssFeed: true,
            tier: {
                id: 'EUR05',
                priceCents: 500,
                hidden: false,
                default: false,
                currency: Currency.EUR,
            },
            categories: [
                {
                    id: 'category-id',
                    name: 'category-name',
                    slug: 'slug',
                },
            ],
            isDeleted: false,
            privacyPolicyEnabled: false,
            analytics: {
                facebookPixelId: 'facebook-pixel-id',
            },
            hasGiftsAllowed: true,
            emailPublic: '<EMAIL>',
            profileType: UserProfileType.PUBLIC,
        },
    ],
    participantIds: ['hunghoangzfgvmdem', 'eliskaslaharovanojvddav'],
    createdAt: '2023-05-10T12:12:11.844280Z',
    seenAt: '2023-10-30T17:10:47.109783Z',
    checkedAt: '2023-11-30T17:10:47.109783Z',
    lastMessageAt: '2023-12-30T17:10:47.109783Z',
    lastMessage: {
        id: 'post-id',
        messageThreadId: 'message-thread-id',
        text: 'post-text',
        textHtml: '<h1>Header</h1>',
        sentAt: '2023-09-17T00:00:00Z',
        sentById: 'user-id',
        price: 10,
        fullAssets: false,
        assets: [
            {
                url: 'image-url',
                width: 700,
                height: 600,
                assetType: 'image',
            },
            {
                name: 'document-name',
                url: 'document-url',
                type: PostDocumentType.DOCX,
                assetType: 'document',
            },
            {
                assetType: 'gjirafa',
                hidden: false,
                hasAudio: true,
                hasVideo: true,
                key: 'sfyMS3YYBbCjjZhQG4D7Ig==,7991601123',
                keyId: '20FFB244-27C5-0C40-A00B-D28096FF7216',
                id: 'id',
                duration: 45.1,
                width: 0,
                height: 0,
                progressTillCompleteness: 0,
                progressTillReadiness: 0,
                status: GjirafaQualityTypeStatus.COMPLETE,
                previewStaticUrl:
                    'https://cdn.vpplayer.tech/agmipobm/tNTT3UXuKWZTxrJpT36D0w==,4837254998/images/vjsnqosr/original-thumbnail.webp',
                previewAnimatedUrl:
                    'https://cdn.vpplayer.tech/agmipobm/tNTT3UXuKWZTxrJpT36D0w==,4837254998/images/vjsnqosr/original-animated.webp',
                previewStripUrl:
                    'https://cdn.vpplayer.tech/agmipobm/epDKgAZEDoALFiwtjVcmIg==,7991601123/encode/vjsnxsqt/thumbnails/filmstrip.vtt',
                thumbnailUrl: 'thumbnail',
                thumbnail: {
                    url: 'thumbnail',
                    width: 700,
                    height: 600,
                },
                isLivestreamRecording: false,
                timestamp: 420.0,
            },
            {
                id: 'vdjnejr',
                status: GjirafaLivestreamStatus.LIVE,
                channelPublicId: 'adqmnej',
                playbackUrl: 'playback-url',
                assetType: 'gjirafa-livestream',
                thumbnailUrl: 'thumbnail',
                thumbnail: {
                    url: 'thumbnail',
                    width: 700,
                    height: 600,
                },
            },
            {
                id: 'youtube-id',
                thumbnailUrl: 'thumbnail-youtube',
                assetType: 'youtube',
                thumbnail: {
                    url: 'thumbnail',
                    width: 700,
                    height: 600,
                },
            },
            {
                assetType: 'empty',
                dummy: 'empty',
            },
        ],
    },
}

const expectedMessage: MessageModel = {
    id: 'post-id',
    messageThreadId: 'message-thread-id',
    text: 'post-text',
    textHtml: '<h1>Header</h1>',
    sentAt: '2023-09-17T00:00:00Z',
    sentById: 'user-id',
    price: 10,
    fullAssets: false,
    assets: [
        {
            url: 'image-url',
            width: 700,
            height: 600,
            assetType: 'image',
        },
        {
            name: 'document-name',
            url: 'document-url',
            type: PostDocumentType.DOCX,
            assetType: 'document',
        },
        {
            hidden: false,
            hasAudio: true,
            hasVideo: true,
            id: 'id',
            duration: 45.1,
            key: 'sfyMS3YYBbCjjZhQG4D7Ig==,7991601123',
            keyId: '20FFB244-27C5-0C40-A00B-D28096FF7216',
            width: 0,
            height: 0,
            progressTillCompleteness: 0,
            progressTillReadiness: 0,
            assetType: 'gjirafa',
            status: GjirafaQualityTypeStatus.COMPLETE,
            previewStaticUrl:
                'https://cdn.vpplayer.tech/agmipobm/tNTT3UXuKWZTxrJpT36D0w==,4837254998/images/vjsnqosr/original-thumbnail.webp',
            previewAnimatedUrl:
                'https://cdn.vpplayer.tech/agmipobm/tNTT3UXuKWZTxrJpT36D0w==,4837254998/images/vjsnqosr/original-animated.webp',
            previewStripUrl:
                'https://cdn.vpplayer.tech/agmipobm/epDKgAZEDoALFiwtjVcmIg==,7991601123/encode/vjsnxsqt/thumbnails/filmstrip.vtt',
            thumbnailUrl: 'thumbnail',
            thumbnail: {
                url: 'thumbnail',
                width: 700,
                height: 600,
            },
            isLivestreamRecording: false,
            timestamp: 420.0,
        },
        {
            id: 'vdjnejr',
            status: GjirafaLivestreamStatus.LIVE,
            channelPublicId: 'adqmnej',
            playbackUrl: 'playback-url',
            assetType: 'gjirafa-livestream',
            thumbnailUrl: 'thumbnail',
            thumbnail: {
                url: 'thumbnail',
                width: 700,
                height: 600,
            },
        },
        {
            id: 'youtube-id',
            thumbnailUrl: 'thumbnail-youtube',
            assetType: 'youtube',
            thumbnail: {
                url: 'thumbnail',
                width: 700,
                height: 600,
            },
        },
        {
            assetType: 'empty',
            dummy: 'empty',
        },
    ],
}

function messageThreadDetailsResponse(): MessageThreadDetailsResponse {
    return {
        id: '1683720731840-1319805956',
        participants: [],
        participantIds: ['hunghoangzfgvmdem', 'eliskaslaharovanojvddav'],
        createdAt: '2023-05-10T12:12:11.844280Z',
        seenAt: '2023-10-30T17:10:47.109783Z',
        checkedAt: '2023-11-29T17:10:47.109783Z',
        lastMessageAt: '2023-12-28T17:10:47.109783Z',
        relation: SubscriptionRelationType.GROUP,
        canPost: true,
        commonCreators: ['cestmir'],
        deleted: false,
        archived: false,
    }
}

function pagedMessageThreadsResponse(): PagedMessageThreadsResponse {
    const messageThread: MessageThreadResponse = {
        id: '1683720731840-1319805956',
        participants: [userResponse('hunghoangzfgvmdem')],
        participantIds: ['hunghoangzfgvmdem', 'eliskaslaharovanojvddav'],
        createdAt: '2023-05-10T12:12:11.844280Z',
        seenAt: '2023-10-30T17:10:47.109783Z',
        checkedAt: '2023-11-30T17:10:47.109783Z',
        lastMessageAt: '2023-12-30T17:10:47.109783Z',
        deletedAt: undefined,
        deleted: false,
        archived: false,
        lastMessage: postResponse(),
        canMessage: false,
    }

    return {
        content: [messageThread],
        hasNext: false,
        afterCursor: 'eyJ1c2VySWQiOiJodW5naG9hbmd6Zmd2bWRlbSIsImxh',
    }
}
