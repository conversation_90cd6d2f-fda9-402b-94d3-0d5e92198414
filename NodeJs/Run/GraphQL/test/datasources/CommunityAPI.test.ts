import nock from 'nock'
import { Environment } from '../../src/common/environment'
import { CommunityAPI } from '../../src/datasources/CommunityAPI'
import { CommunityResponse, CommunityType, CommunityType as RestCommunityType } from '../../src/generated/api'
import { CommunityModel } from '../../src/models/community'
import { CommunitySortFields, Currency, SortDirection, UserProfileType } from '../../src/generated/resolvers-types'
import { userResponse } from './test-utils'

beforeEach(() => {
    nock.disableNetConnect()
})

describe('data source: CommunityAPI', () => {
    describe('method: getCommunity', () => {
        test('should make a call to get a community and correctly map it', async () => {
            // given
            const communityId = 'community-id'
            const underTest = new CommunityAPI(Environment.DEVEL)
            const scope = nock('https://devel-api-90568653510.europe-west1.run.app')
                .get(`/v1/communities/${communityId}`)
                .reply(200, communityResponse(communityId))

            // when
            const community = await underTest.getCommunity({ communityId })

            // then
            expect(community).toEqual<CommunityModel>({
                id: communityId,
                name: 'Test Community',
                description: 'A test community description',
                slug: 'test-community',
                ownerId: 'owner-id',
                owner: {
                    id: 'owner-id',
                    name: 'user-name',
                    bio: 'user-bio',
                    image: {
                        url: 'image-id',
                        width: 420,
                        height: 69,
                        hidden: true,
                    },
                    path: 'user-path',
                    subscribable: true,
                    verified: false,
                    counts: {
                        supporting: 150,
                        supporters: 21231,
                        supportersThreshold: 25000,
                        posts: 243,
                        ownedCommunities: 1,
                    },
                    hasRssFeed: true,
                    spotifyShowUri: 'spotify-uri',
                    tier: {
                        id: 'EUR05',
                        priceCents: 500,
                        hidden: false,
                        default: false,
                        currency: Currency.EUR,
                    },
                    categories: [
                        {
                            id: 'category-id',
                            name: 'category-name',
                            slug: 'slug',
                        },
                    ],
                    isDeleted: false,
                    privacyPolicyEnabled: false,
                    analytics: {
                        facebookPixelId: 'facebook-pixel-id',
                    },
                    hasGiftsAllowed: true,
                    emailPublic: '<EMAIL>',
                    profileType: UserProfileType.PUBLIC,
                },
                membersCount: 42,
                image: {
                    url: 'community-image-id',
                    width: 800,
                    height: 600,
                    hidden: false,
                },
                createdAt: '2023-07-17T00:00:00Z',
                slugEditableAfter: '2024-07-17T00:00:00Z',
                isVerified: true,
                isMember: true,
                threadsCount: 10,
                type: CommunityType.FREE,
            })
            expect(scope.isDone()).toBeTruthy()
        })
    })

    describe('method: postCommunities', () => {
        test('should make a call to create a community and correctly map it', async () => {
            // given
            const communityId = 'new-community-id'
            const underTest = new CommunityAPI(Environment.DEVEL)
            const scope = nock('https://devel-api-90568653510.europe-west1.run.app')
                .post('/v1/communities')
                .reply(200, communityResponse(communityId))

            // when
            const community = await underTest.postCommunities()

            // then
            expect(community).toEqual<CommunityModel>({
                id: communityId,
                name: 'Test Community',
                description: 'A test community description',
                slug: 'test-community',
                ownerId: 'owner-id',
                owner: {
                    id: 'owner-id',
                    name: 'user-name',
                    bio: 'user-bio',
                    image: {
                        url: 'image-id',
                        width: 420,
                        height: 69,
                        hidden: true,
                    },
                    path: 'user-path',
                    subscribable: true,
                    verified: false,
                    counts: {
                        supporting: 150,
                        supporters: 21231,
                        supportersThreshold: 25000,
                        posts: 243,
                        ownedCommunities: 1,
                    },
                    hasRssFeed: true,
                    spotifyShowUri: 'spotify-uri',
                    tier: {
                        id: 'EUR05',
                        priceCents: 500,
                        hidden: false,
                        default: false,
                        currency: Currency.EUR,
                    },
                    categories: [
                        {
                            id: 'category-id',
                            name: 'category-name',
                            slug: 'slug',
                        },
                    ],
                    isDeleted: false,
                    privacyPolicyEnabled: false,
                    analytics: {
                        facebookPixelId: 'facebook-pixel-id',
                    },
                    hasGiftsAllowed: true,
                    emailPublic: '<EMAIL>',
                    profileType: UserProfileType.PUBLIC,
                },
                membersCount: 42,
                image: {
                    url: 'community-image-id',
                    width: 800,
                    height: 600,
                    hidden: false,
                },
                createdAt: '2023-07-17T00:00:00Z',
                slugEditableAfter: '2024-07-17T00:00:00Z',
                isVerified: true,
                isMember: true,
                threadsCount: 10,
                type: CommunityType.FREE,
            })

            expect(scope.isDone()).toBeTruthy()
        })
    })

    describe('method: getCommunities', () => {
        test('should make a call to get communities for an owner and correctly map them', async () => {
            // given
            const ownerId = 'owner-id'
            const underTest = new CommunityAPI(Environment.DEVEL)
            const scope = nock('https://devel-api-90568653510.europe-west1.run.app')
                .get(
                    `/v1/communities?ownerId=${ownerId}&isMember=true&query=bum%20bam&sortBy=CREATED_AT&sortDirection=DESC`
                )
                .reply(200, {
                    content: [communityResponse('community-1'), communityResponse('community-2')],
                    hasNext: true,
                    afterCursor: 'after-cursor',
                })

            // when
            const { communities, pagination } = await underTest.getCommunities({
                ownerId,
                isMember: true,
                query: 'bum bam',
                sort: {
                    by: CommunitySortFields.CREATED_AT,
                    order: SortDirection.DESC,
                },
            })

            expect(pagination).toEqual({
                hasNextPage: true,
                endCursor: 'after-cursor',
                startCursor: undefined,
            })

            // then
            expect(communities).toHaveLength(2)
            expect(communities[0]).toEqual<CommunityModel>({
                id: 'community-1',
                name: 'Test Community',
                description: 'A test community description',
                slug: 'test-community',
                ownerId: 'owner-id',
                owner: {
                    id: 'owner-id',
                    name: 'user-name',
                    bio: 'user-bio',
                    image: {
                        url: 'image-id',
                        width: 420,
                        height: 69,
                        hidden: true,
                    },
                    path: 'user-path',
                    subscribable: true,
                    verified: false,
                    counts: {
                        supporting: 150,
                        supporters: 21231,
                        supportersThreshold: 25000,
                        posts: 243,
                        ownedCommunities: 1,
                    },
                    hasRssFeed: true,
                    spotifyShowUri: 'spotify-uri',
                    tier: {
                        id: 'EUR05',
                        priceCents: 500,
                        hidden: false,
                        default: false,
                        currency: Currency.EUR,
                    },
                    categories: [
                        {
                            id: 'category-id',
                            name: 'category-name',
                            slug: 'slug',
                        },
                    ],
                    isDeleted: false,
                    privacyPolicyEnabled: false,
                    analytics: {
                        facebookPixelId: 'facebook-pixel-id',
                    },
                    hasGiftsAllowed: true,
                    emailPublic: '<EMAIL>',
                    profileType: UserProfileType.PUBLIC,
                },
                membersCount: 42,
                image: {
                    url: 'community-image-id',
                    width: 800,
                    height: 600,
                    hidden: false,
                },
                createdAt: '2023-07-17T00:00:00Z',
                slugEditableAfter: '2024-07-17T00:00:00Z',
                isVerified: true,
                isMember: true,
                threadsCount: 10,
                type: CommunityType.FREE,
            })
            expect(communities[1].id).toBe('community-2')
            expect(scope.isDone()).toBeTruthy()
        })
    })

    describe('method: putCommunities', () => {
        test('should make a call to update a community and correctly map it', async () => {
            // given
            const communityId = 'community-id'
            const updateRequest = {
                name: 'Updated Community',
                description: 'Updated description',
                slug: 'updated-slug',
                image: {
                    id: 'new-image-id',
                    width: 1000,
                    height: 800,
                    fileName: null,
                    fileSize: null,
                    hidden: false,
                },
                type: RestCommunityType.FREE,
            }
            const underTest = new CommunityAPI(Environment.DEVEL)
            const scope = nock('https://devel-api-90568653510.europe-west1.run.app')
                .put(`/v1/communities/${communityId}`, updateRequest)
                .reply(200, {
                    ...communityResponse(communityId),
                    name: 'Updated Community',
                    description: 'Updated description',
                    slug: 'updated-slug',
                    image: {
                        id: 'new-image-id',
                        width: 1000,
                        height: 800,
                        hidden: false,
                    },
                })

            // when
            const community = await underTest.putCommunities(communityId, updateRequest)

            // then
            expect(community.id).toBe(communityId)
            expect(community.name).toBe('Updated Community')
            expect(community.description).toBe('Updated description')
            expect(community.slug).toBe('updated-slug')
            expect(community.image?.url).toBe('new-image-id')
            expect(community.image?.width).toBe(1000)
            expect(community.image?.height).toBe(800)
            expect(scope.isDone()).toBeTruthy()
        })
    })

    describe('method: postCommunityMembers', () => {
        test('should make a call to join a community', async () => {
            // given
            const communityId = 'community-id'
            const underTest = new CommunityAPI(Environment.DEVEL)
            const scope = nock('https://devel-api-90568653510.europe-west1.run.app')
                .post(`/v1/communities/${communityId}/members`)
                .reply(204)

            // when
            await underTest.postCommunityMembers({ communityId })

            // then
            expect(scope.isDone()).toBeTruthy()
        })
    })

    describe('method: deleteCommunityMembers', () => {
        test('should make a call to leave a community', async () => {
            // given
            const communityId = 'community-id'
            const userId = 'user-id'
            const underTest = new CommunityAPI(Environment.DEVEL)
            const scope = nock('https://devel-api-90568653510.europe-west1.run.app')
                .delete(`/v1/communities/${communityId}/members/${userId}`)
                .reply(204)

            // when
            await underTest.deleteCommunityMembers({ communityId, userId })

            // then
            expect(scope.isDone()).toBeTruthy()
        })
    })
})

function communityResponse(communityId: string): CommunityResponse {
    return {
        id: communityId,
        name: 'Test Community',
        description: 'A test community description',
        slug: 'test-community',
        ownerId: 'owner-id',
        owner: userResponse('owner-id'),
        membersCount: 42,
        image: {
            id: 'community-image-id',
            width: 800,
            height: 600,
            hidden: false,
        },
        createdAt: '2023-07-17T00:00:00Z',
        isVerified: true,
        isMember: true,
        threadsCount: 10,
        slugEditableAfter: '2024-07-17T00:00:00Z',
        type: RestCommunityType.FREE,
    }
}
