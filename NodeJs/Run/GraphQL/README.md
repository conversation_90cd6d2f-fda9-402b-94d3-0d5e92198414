# GraphQL Service

This GraphQL service acts as a Backend for Frontend (BFF), aggregating and orchestrating calls to our Kotlin backend services. It provides a unified GraphQL API that simplifies client interactions by consolidating multiple REST API calls into single GraphQL queries and mutations.

## Architecture

The service aggregates data from multiple backend services including API, Media, and Auth services, providing a cohesive GraphQL interface for frontend applications. It handles authentication, data transformation, and optimizes network requests through GraphQL's efficient query capabilities.

## Development

### Prerequisites
- Node.js 18+
- npm or yarn

### Setup

1. **Install dependencies**
   ```bash
   npm install
   ```

2. **Create environment file**
   ```bash
   cat <<EOT >> .env
   GJIRAFA_API_KEY="your_gjirafa_api_key"
   EOT
   ```

3. **Start development server**
   ```bash
   npm run dev
   ```

### Type Generation

#### Generate GraphQL Types from Schema
Generate TypeScript types from the GraphQL schema:
```bash
npm run codegen
```
This reads from `schema.graphql` and generates resolver types in `src/generated/resolvers-types.ts`.

#### Generate Types from Kotlin Backend
Generate TypeScript models from the Kotlin backend OpenAPI specifications:

**For main API service:**
```bash
npm run generate-api-model
```

**For Gjirafa service:**
```bash
npm run generate-gjirafa-model
```

These commands generate TypeScript models in `src/generated/api/` and `src/generated/gjirafa/` respectively.

### Available Scripts
- `npm run dev` - Start development server with hot reload
- `npm run compile` - Compile TypeScript to JavaScript
- `npm run start` - Build and start production server
- `npm test` - Run test suite
- `npm run lint` - Run ESLint
- `npm run format` - Check code formatting with Prettier