import { MessageThreadResolvers } from '../generated/resolvers-types'

export const messageThreadResolvers: MessageThreadResolvers = {
    participants: async ({ participants, participantIds }, _, { dataSources }) => {
        if (participants.length != 0) {
            return participants
        }

        return await Promise.all(participantIds.map((id) => dataSources.userAPI.getUserOrDeleted(id)))
    },

    lastMessage: async ({ lastMessage, lastMessageId }, _, { dataSources }) => {
        if (lastMessage) {
            return lastMessage
        }

        if (!lastMessageId) {
            return null
        }

        return await dataSources.messageThreadAPI.getMessage(lastMessageId)
    },
}
