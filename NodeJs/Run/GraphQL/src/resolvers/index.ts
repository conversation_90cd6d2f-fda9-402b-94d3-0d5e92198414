import { Resolvers } from '../generated/resolvers-types'
import { messageAssetResolvers, messageResolvers, queries } from './queries'
import { mutations } from './mutations'
import { BigIntResolver, DateTimeResolver, VoidResolver } from 'graphql-scalars'
import { makeExecutableSchema } from '@graphql-tools/schema'
import { readFileSync } from 'fs'
import {
    commentNotificationResolvers,
    communityCommentNotificationResolvers,
    communityThreadNotificationResolvers,
    messageNotificationResolvers,
    notificationResolvers,
    postNotificationResolvers,
    requestNotificationResolvers,
    subscriberNotificationResolvers,
    subscriptionNotificationResolvers,
} from './notification'
import {
    completeContentPostResolver,
    limitedContentPostResolvers,
    postAssetResolvers,
    postGjirafaAssetResolvers,
    postGjirafaLivestreamAssetResolvers,
    postPreviewAssetResolvers,
    postResolvers,
    previewContentPostResolvers,
} from './post'
import { commentParentResolver, commentResolvers } from './comment'
import { subscribeRequestResolvers, userSubscriptionDetailsResolvers, userSubscriptionResolvers } from './subscription'
import { livestreamDetailsResolvers, userDetailsResolvers, userResolvers } from './user'
import { messageThreadResolvers } from './message'

export const resolvers: Resolvers = {
    Query: queries,
    Mutation: mutations,
    Notification: notificationResolvers,
    CommunityCommentNotification: communityCommentNotificationResolvers,
    CommunityThreadNotification: communityThreadNotificationResolvers,
    PostNotification: postNotificationResolvers,
    SubscriberNotification: subscriberNotificationResolvers,
    SubscribeRequest: subscribeRequestResolvers,
    SubscriptionNotification: subscriptionNotificationResolvers,
    RequestNotification: requestNotificationResolvers,
    CommentNotification: commentNotificationResolvers,
    MessageNotification: messageNotificationResolvers,
    MessageAsset: messageAssetResolvers,
    MessageThread: messageThreadResolvers,
    PostAsset: postAssetResolvers,
    PostPreviewAsset: postPreviewAssetResolvers,
    Message: messageResolvers,
    DateTime: DateTimeResolver,
    Void: VoidResolver,
    Post: postResolvers,
    PostGjirafaAsset: postGjirafaAssetResolvers,
    PostGjirafaLivestreamAsset: postGjirafaLivestreamAssetResolvers,
    CompleteContentPost: completeContentPostResolver,
    LimitedContentPost: limitedContentPostResolvers,
    PreviewContentPost: previewContentPostResolvers,
    Comment: commentResolvers,
    CommentParent: commentParentResolver,
    User: userResolvers,
    UserDetails: userDetailsResolvers,
    BigInt: BigIntResolver,
    UserSubscription: userSubscriptionResolvers,
    UserSubscriptionDetails: userSubscriptionDetailsResolvers,
    LivestreamDetails: livestreamDetailsResolvers,
}

const typeDefs = readFileSync('./schema.graphql', { encoding: 'utf-8' })

export const schema = makeExecutableSchema({
    typeDefs,
    resolvers,
    inheritResolversFromInterfaces: true,
})
