import { ApolloServer } from '@apollo/server'
import { contextFunction, DataSourceContext } from './context'
import { currentEnv, Environment } from './common/environment'
import { expressMiddleware } from '@apollo/server/express4'
import express from 'express'
import http from 'http'
import cors, { CorsOptions } from 'cors'
import bodyParser from 'body-parser'
import { ApolloServerPluginDrainHttpServer } from '@apollo/server/plugin/drainHttpServer'
import { apolloLoggerPlugin } from './plugins/error-logging'
import * as Sentry from '@sentry/node'
import { apolloSentryPlugin } from './plugins/sentry'
import { schema } from './resolvers'
import { securityPlugins, validationRules } from './plugins/security'
import { useHive } from '@graphql-hive/apollo'
import { logger } from './common/logger'

const app = express()
const httpServer = http.createServer(app)

const server = new ApolloServer<DataSourceContext>({
    schema,
    plugins: [
        useHive({
            enabled: ![Environment.TEST, Environment.LOCAL, Environment.PRODUCTION].includes(currentEnv),
            token: process.env.HIVE_API_KEY,
            usage: {
                sampleRate: currentEnv == Environment.PRODUCTION ? 0 : 1.0,
            },
        }),
        ...securityPlugins,
        ApolloServerPluginDrainHttpServer({ httpServer }),
        apolloLoggerPlugin,
        apolloSentryPlugin,
    ],
    validationRules: [...validationRules],
    includeStacktraceInErrorResponses: false,
    formatError: (formattedError) => {
        const { extensions } = formattedError
        excludeUrlFromError(extensions)

        return formattedError
    },
    introspection: currentEnv != Environment.PRODUCTION,
})

const excludeUrlFromError = (extensions: Record<string, unknown> | undefined) => {
    const extResponse = extensions?.response
    if (extResponse && typeof extResponse === 'object' && 'url' in extResponse) {
        const { url, ...rest } = extResponse
        extensions.response = rest
    }
}

Sentry.init({
    dsn: process.env.SENTRY_DSN,
    environment: currentEnv,
    integrations: [Sentry.httpIntegration(), Sentry.requestDataIntegration()],
    tracesSampleRate: 1.0,
    debug: false,
    sendClientReports: false,
    beforeSend: (event) => {
        event.modules = undefined
        return event
    },
    beforeBreadcrumb: (breadcrumb) => {
        const url = breadcrumb.data?.url as string | undefined
        if (url?.includes('/computeMetadata/v1')) {
            return null
        }
        return breadcrumb
    },
})

const origins = process.env.ORIGIN_POLICY?.split(';') ?? []
const corsOptions: CorsOptions = {
    origin: origins,
    credentials: true,
    maxAge: 7200,
}

const port = parseInt(process.env.PORT ?? '4000')
server
    .start()
    .then(() => {
        app.use(
            '/',
            cors(corsOptions),
            bodyParser.json(),
            Sentry.expressErrorHandler(),
            expressMiddleware(server, {
                context: contextFunction({
                    server,
                    env: currentEnv,
                    gjirafa: {
                        apiKey: process.env.GJIRAFA_API_KEY,
                        projectId: process.env.GJIRAFA_PROJECT,
                    },
                    internalApiKey: process.env.INTERNAL_API_KEY,
                }),
            })
        )

        httpServer.listen({ port }, () => {
            logger.info(`🚀 Server started on port ${port} with environment set to ${currentEnv}`)
        })
    })
    .catch((err: unknown) => {
        logger.error('Failed to start server', err)
    })
