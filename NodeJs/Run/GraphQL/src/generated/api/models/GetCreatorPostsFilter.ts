/* generated using openapi-typescript-codegen -- do not edit */
/* istanbul ignore file */
/* tslint:disable */
/* eslint-disable */
import type { PostFilterType } from './PostFilterType'
import type { PostSource } from './PostSource'

export type GetCreatorPostsFilter = {
    type?: PostFilterType;
    categoryId?: string | null;
    communityId?: string | null;
    source?: PostSource | null;
    query?: string | null;
    excludedCreatorIds: Array<string>;
};

