import { ImageAsset, UserModel } from './user'
import { CommunityType } from '../generated/resolvers-types'

export type CommunityModel = {
    id: string
    name: string
    description: string
    slug: string
    ownerId: string
    owner: UserModel
    membersCount: number
    image?: ImageAsset | null
    createdAt: string
    isVerified: boolean
    isMember: boolean
    threadsCount: number
    slugEditableAfter: string
    type: CommunityType
}
