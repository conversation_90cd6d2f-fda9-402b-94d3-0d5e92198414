import { UserCompanyVatType, UserProfileType, UserRole } from '../generated/resolvers-types'
import { TierModel } from './subscription'

export type UserModel = {
    id: string
    name: string
    bio: string
    path: string
    image?: ImageAsset | null
    hasRssFeed: boolean
    counts: UserCounts
    verified: boolean
    subscribable: boolean
    tier: TierModel
    categories: CategoryModel[]
    isDeleted: boolean
    privacyPolicyEnabled: boolean
    analytics?: UserAnalyticsModel
    hasGiftsAllowed: boolean
    spotifyShowUri?: string | null
    emailPublic?: string | null
    profileType: UserProfileType
}

export type UserDetailsModel = {
    id: string
    name: string
    bio: string
    path: string
    pathEditableAfter: string
    image?: ImageAsset | null
    hasRssFeed: boolean
    counts: UserDetailsCounts
    verified: boolean
    subscribable: boolean
    tier: TierModel
    categories: CategoryModel[]
    email?: string
    creator?: UserCreatorModel | null
    discord?: DiscordModel | null
    notificationSettings: UserNotificationSettings
    language: string
    role: UserRole
    spotify?: UserSpotifyDetailsModel
    livestream?: LivestreamDetailsModel
    isOfAge: boolean
    emailPublic?: string | null
    emailInvoice?: string | null
    profileType: UserProfileType
    hasPostPreviews: boolean
    hasWelcomeMessageEnabled: boolean
}

export type LivestreamDetailsModel = {
    streamUrl: string
    streamKey: string
    publicId: string
}

export type UserSpotifyDetailsModel = {
    podcastUri?: string | null
    isConnected: boolean
}

export type UserAnalyticsModel = {
    facebookPixelId?: string | null
    ga4Stream?: string | null
    googleAdsConversionId?: string | null
    googleAdsConversionLabel?: string | null
    tiktokPixelId?: string | null
    leadHub?: string | null
}

export type UserCreatorModel = {
    stripeAccountOnboarded: boolean
    stripeAccountActive: boolean
    stripeAccountId: string
    stripeRequirements?: StripeRequirements | null
}

export type StripeRequirements = {
    stripeAccountId?: string | null
    deleted: boolean
    disabledReason?: string | null
    currentlyDue: string[]
    eventuallyDue: string[]
    pastDue: string[]
    errors: string[]
    pendingVerification: string[]
    valid: boolean
}

export type DiscordModel = {
    id: string
    guildId?: string | null
}

export type CategoryModel = {
    id: string
    name: string
    slug: string
}

export type UserCompany = {
    name?: string | null
    address?: string | null
    postalCode?: string | null
    country?: string | null
    city?: string | null
    id?: string | null
    vatId?: string | null
    additionalInfo?: string | null
    registeredWith?: string | null
    vatType: UserCompanyVatType
    vatRate?: number | null
    iban?: string | null
    swift?: string | null
}

export type UserNotificationSettings = {
    emailNewPost: boolean
}

export type UserAnalytics = {
    facebookPixelId?: string
}

export type UserDetailsCounts = {
    supporters: number
    supporting: number
    incomes: number
    incomesClean: number
    payments: number
    posts: number
    invoices: number
    pendingRequests: number
}

export type UserCounts = {
    supporters: number
    supportersThreshold: number | null | undefined
    supporting: number
    posts: number
    ownedCommunities: number
}

export type ImageAsset = {
    url: string
    width: number
    height: number
    hidden: boolean
    fileName?: string | null
    fileSize?: number | null
}

export type AssetTimestamps = Record<string, number>
