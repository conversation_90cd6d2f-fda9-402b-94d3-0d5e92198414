import { UserModel } from './user'
import { StorageEntityType } from '../generated/api'

export type NotificationModel = {
    id: string
    type: NotificationType
    actorCount: number
    createdAt: string
    seenAt?: string | null
    checkedAt?: string | null
    objectId?: string | null
    objectType: StorageEntityType
    lastActorId: string
    lastActor?: UserModel | null
    communityId?: string | null
}

export enum NotificationType {
    CANCELLED_SUBSCRIPTION_BY_CREATOR = 'CANCELLED_SUBSCRIPTION_BY_CREATOR',
    CANCELLED_SUBSCRIPTION_ENDED = 'CANCELLED_SUBSCRIPTION_ENDED',
    CANCELLED_SUBSCRIPTION_INSUFFICIENT_FUNDS = 'CANCELLED_SUBSCRIPTION_INSUFFICIENT_FUNDS',
    CANCELLED_SUBSCRIPTION_OTHER = 'CANCELLED_SUBSCRIPTION_OTHER',
    CANCELLED_SUBSCRIPTION_REFUNDED = 'CANCELLED_SUBSCRIPTION_REFUNDED',
    CANCELLED_SUBSCRIPTION_REFUSED = 'CANCELLED_SUBSCRIPTION_REFUSED',
    NEW_COMMENT = 'NEW_COMMENT',
    NEW_POST = 'NEW_POST',
    NEW_THREAD = 'NEW_THREAD',
    NEW_LIVESTREAM = 'NEW_LIVESTREAM',
    NEW_REPLY = 'NEW_REPLY',
    NEW_REPLY_TO_REPLY = 'NEW_REPLY_TO_REPLY',
    NEW_SUBSCRIPTION = 'NEW_SUBSCRIPTION',
    SUBSCRIBE_REQUEST_ACCEPTED = 'SUBSCRIBE_REQUEST_ACCEPTED',
    PAID_POST = 'PAID_POST',
    PAYMENT_CARD_DECLINED = 'PAYMENT_CARD_DECLINED',
    PAYMENT_FAILED = 'PAYMENT_FAILED',
    PAYMENT_INSUFFICIENT_FUNDS = 'PAYMENT_INSUFFICIENT_FUNDS',
}
