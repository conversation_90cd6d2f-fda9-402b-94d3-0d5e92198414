import { Currency, SubscriptionCouponMethod, SubscriptionStatus } from '../generated/resolvers-types'
import { UserModel } from './user'

export type SubscriptionModel = FullSubscriptionModel | LimitedSubscriptionModel

export type FullSubscriptionModel = {
    id: string
    subscribedAt: string
    status: SubscriptionStatus
    cancelAtPeriodEnd: boolean
    expires?: string | null
    couponAppliedForMonths?: number | null
    couponAppliedForDays?: number | null
    couponExpiresAt?: string | null
    couponPercentOff?: number | null
    couponMethod?: SubscriptionCouponMethod | null
    tier?: TierModel | null
    subscriber: UserModel
    creator: UserModel
    subscriptionModelType: 'full'
    isAppleSubscription: boolean
    applePaidPrice: PriceModel | undefined | null
}

export type PriceModel = {
    priceCents: number
    currency: Currency
}

export type LimitedSubscriptionModel = {
    id: string
    subscribedAt: string
    subscriber: UserModel
    creator: UserModel
    subscriptionModelType: 'limited'
}

export type TierModel = {
    id: string
    priceCents: number
    currency: Currency
    hidden: boolean
    default: boolean
}

export type SubscribeRequestModel = {
    id: number
    userId: string
    creatorId: string
    createdAt: string
    acceptedAt?: string | null
    declinedAt?: string | null
    deletedAt?: string | null
    seenAt?: string | null
}
