import { ServiceDataSource } from './ServiceDataSource'
import { Environment } from '../common/environment'
import { DataSourceConfig } from '@apollo/datasource-rest'
import { NotificationsEnabled, NotificationSettingsUpdateRequest } from '../generated/api'
import { NotificationSettings } from '../generated/resolvers-types'

export class NotificationSettingsAPI extends ServiceDataSource {
    constructor(environment: Environment, cookies?: string, config?: DataSourceConfig) {
        super(environment, 'api', cookies, config)
    }

    async getNotificationSettings(): Promise<NotificationSettings> {
        return this.get<NotificationsEnabled>(`/v1/notification-settings`)
    }

    async updateNotificationSettings(settings: {
        emailNewPost: boolean
        emailNewDm: boolean
        pushNewPost: boolean
        pushNewComment: boolean
        pushNewMessage: boolean
        newsletter: boolean
        termsChanged: boolean
    }) {
        const request: NotificationSettingsUpdateRequest = settings

        await this.put(`/v1/notification-settings`, { body: request })
    }
}
