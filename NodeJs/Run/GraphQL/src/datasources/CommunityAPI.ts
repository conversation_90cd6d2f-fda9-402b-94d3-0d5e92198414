import { ServiceDataSource } from './ServiceDataSource'
import { Environment } from '../common/environment'
import { DataSourceConfig } from '@apollo/datasource-rest'
import { CommunityModel } from '../models/community'
import { CommunityResponse, PagedCommunityResponse, UpdateCommunityRequest } from '../generated/api'
import { mapToUser } from './common-mappers'
import { PaginationModel } from '../models/pagination'
import { PaginationParams, paginationParamsToQueryParams } from './common-utils'
import { CommunitySortFields, SortDirection } from '../generated/resolvers-types'

export class CommunityAPI extends ServiceDataSource {
    constructor(environment: Environment, cookies?: string, config?: DataSourceConfig) {
        super(environment, 'api', cookies, config)
    }

    async getCommunity({
        communityId,
        slug,
    }: {
        communityId?: string | null
        slug?: string | null
    }): Promise<CommunityModel> {
        let response
        if (slug) {
            response = await this.get<CommunityResponse>(`/v1/communities/${slug}?isSlug=true`)
        } else if (communityId) {
            response = await this.get<CommunityResponse>(`/v1/communities/${communityId}`)
        } else {
            throw new Error('Either communityId or slug must be provided')
        }

        return this.mapToCommunity(response)
    }

    async postCommunities(): Promise<CommunityModel> {
        const response = await this.post<CommunityResponse>('/v1/communities')

        return this.mapToCommunity(response)
    }

    async putCommunities(communityId: string, input: UpdateCommunityRequest): Promise<CommunityModel> {
        const response = await this.put<CommunityResponse>(`/v1/communities/${communityId}`, { body: input })

        return this.mapToCommunity(response)
    }

    async getCommunities({
        ownerId,
        isMember,
        query,
        paginationParams,
        sort,
    }: {
        ownerId?: string
        isMember?: boolean
        query?: string
        sort?: {
            by?: CommunitySortFields | 'QUERY_SIMILARITY' | null
            order?: SortDirection | null
        }
        paginationParams?: PaginationParams
    }): Promise<{ communities: CommunityModel[]; pagination: PaginationModel }> {
        const params = paginationParamsToQueryParams(paginationParams ?? {})
        if (ownerId) {
            params.append('ownerId', ownerId)
        }

        if (isMember !== undefined) {
            params.append('isMember', isMember.toString())
        }

        if (query) {
            params.append('query', query)
        }

        if (sort?.by) {
            params.append('sortBy', sort.by)
        }

        if (sort?.order) {
            params.append('sortDirection', sort.order)
        }

        const response = await this.get<PagedCommunityResponse>(`/v1/communities`, { params })
        const communities = response.content.map((el) => this.mapToCommunity(el))

        return {
            communities,
            pagination: {
                hasNextPage: response.hasNext,
                endCursor: response.afterCursor,
                startCursor: response.beforeCursor,
            },
        }
    }

    async postCommunityMembers({ communityId }: { communityId: string }) {
        await this.post(`/v1/communities/${communityId}/members`)
    }

    async deleteCommunityMembers({ communityId, userId }: { communityId: string; userId: string }) {
        await this.delete(`/v1/communities/${communityId}/members/${userId}`)
    }

    private mapToCommunity(response: CommunityResponse): CommunityModel {
        return {
            id: response.id,
            name: response.name,
            description: response.description,
            slug: response.slug,
            ownerId: response.ownerId,
            owner: mapToUser(response.owner),
            membersCount: response.membersCount,
            ...(response.image && {
                image: {
                    url: response.image.id,
                    height: response.image.height,
                    width: response.image.width,
                    hidden: response.image.hidden,
                    fileName: response.image.fileName,
                    fileSize: response.image.fileSize,
                },
            }),
            createdAt: response.createdAt,
            isVerified: response.isVerified,
            isMember: response.isMember,
            threadsCount: response.threadsCount,
            slugEditableAfter: response.slugEditableAfter,
            type: response.type,
        }
    }
}
