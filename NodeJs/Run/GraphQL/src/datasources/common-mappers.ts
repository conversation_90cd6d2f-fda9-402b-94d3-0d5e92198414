import {
    Currency as <PERSON><PERSON><PERSON>cy<PERSON><PERSON>,
    DocumentType,
    DocumentType as DocumentTypeRest,
    GjirafaStatus as GjirafaStatusRest,
    LiveVideoStatus,
    NotificationType as NotificationTypeRest,
    PollResponse,
    PostAssetInput as PostAssetInputRest,
    PostAssetResponse,
    PostDto,
    PostPreviewAssetResponse,
    PostResponse,
    PostState as PostStateRest,
    PreviewGjirafaType,
    Role,
    SubscriptionsDtoStatus,
    TierResponse,
    UserResponse,
} from '../generated/api'
import { CommentModel, PollModel, PostAssetModel, PostModel, PreviewAssetModel } from '../models/post'
import {
    Currency,
    GjirafaLivestreamStatus,
    GjirafaQualityTypeStatus,
    PostAssetInput,
    PostDocumentType,
    PostPreviewGjirafaType,
    PostState,
    SubscriptionStatus,
    UserRole,
} from '../generated/resolvers-types'
import { MessageModel } from '../models/message-thread'
import { NotificationType } from '../models/notification'
import { TierModel } from '../models/subscription'
import { UserModel } from '../models/user'
import type { PostAssetInputWithYoutube } from './PostAPI'
import { GraphQLError } from 'graphql/error'
import { ApolloServerErrorCode } from '@apollo/server/errors'

function mapPreviewAsset(asset: PostPreviewAssetResponse): PreviewAssetModel {
    if (asset.image) {
        return {
            url: asset.image.url,
            width: asset.image.width,
            height: asset.image.height,
            assetType: 'preview-image',
        }
    } else if (asset.gjirafa) {
        return {
            previewStaticUrl: asset.gjirafa.previewStaticUrl ?? '',
            previewAnimatedUrl: asset.gjirafa.previewAnimatedUrl,
            previewStripUrl: asset.gjirafa.previewStripUrl,
            duration: asset.gjirafa.duration,
            type: mapPreviewGjirafaAssetType(asset.gjirafa.type),
            thumbnailUrl: asset.thumbnailImage?.url,
            width: asset.gjirafa.width ?? 0,
            height: asset.gjirafa.height ?? 0,
            assetType: 'preview-gjirafa',
        }
    } else if (asset.gjirafaLive) {
        return {
            thumbnailUrl: asset.gjirafaLive.thumbnailImage?.url,
            assetType: 'preview-gjirafa-live',
        }
    } else if (asset.document) {
        return {
            type: mapDocumentType(asset.document.type),
            name: asset.document.name ?? undefined,
            thumbnailUrl: asset.thumbnailImage?.url,
            assetType: 'preview-document',
        }
    } else {
        throw Error('Failed to map asset')
    }
}

function mapPreviewGjirafaAssetType(type: PreviewGjirafaType): PostPreviewGjirafaType {
    switch (type) {
        case PreviewGjirafaType.AUDIO:
            return PostPreviewGjirafaType.AUDIO
        case PreviewGjirafaType.VIDEO:
            return PostPreviewGjirafaType.VIDEO
    }
}

function mapAsset(asset: PostAssetResponse): PostAssetModel {
    if (asset.image) {
        const image = asset.image
        return {
            url: image.url,
            width: image.width,
            height: image.height,
            assetType: 'image',
        }
    } else if (asset.document) {
        const document = asset.document
        return {
            url: document.url,
            type: mapDocumentType(document.type),
            name: document.name ?? '',
            thumbnailUrl: asset.thumbnail,
            thumbnail: asset.thumbnailImage,
            assetType: 'document',
        }
    } else if (asset.gjirafa) {
        const gjirafa = asset.gjirafa
        return {
            progressTillCompleteness: gjirafa.progressTillCompleteness,
            progressTillReadiness: gjirafa.progressTillReadiness,
            width: gjirafa.width ?? 0,
            height: gjirafa.height ?? 0,
            key: gjirafa.key,
            keyId: gjirafa.keyId ?? '',
            id: gjirafa.id,
            videoStreamUrl: gjirafa.videoStreamUrl,
            audioStreamUrl: gjirafa.audioStreamUrl,
            audioStaticUrl: gjirafa.audioStaticUrl,
            audioByteSize: gjirafa.audioByteSize,
            hasVideo: gjirafa.hasVideo,
            hasAudio: gjirafa.hasAudio,
            hidden: gjirafa.hidden,
            duration: gjirafa.duration,
            status: mapGjirafaStatus(gjirafa.status),
            assetType: 'gjirafa',
            thumbnailUrl: asset.thumbnail,
            thumbnail: asset.thumbnailImage,
            previewStaticUrl: gjirafa.previewStaticUrl ?? '',
            previewAnimatedUrl: gjirafa.previewAnimatedUrl,
            previewStripUrl: gjirafa.previewStripUrl,
            isLivestreamRecording: asset.gjirafaLive !== undefined,
            timestamp: asset.timestamp,
        }
    } else if (asset.gjirafaLive) {
        return {
            id: asset.gjirafaLive.id,
            playbackUrl: asset.gjirafaLive.playbackUrl,
            channelPublicId: asset.gjirafaLive.channelPublicId,
            status: mapGjirafaLivestreamStatus(asset.gjirafaLive.liveStatus),
            thumbnailUrl: asset.thumbnail,
            thumbnail: asset.thumbnailImage,
            assetType: 'gjirafa-livestream',
        }
    } else if (asset.youTube) {
        return {
            id: asset.youTube.id,
            thumbnailUrl: asset.thumbnail,
            thumbnail: asset.thumbnailImage,
            width: asset.youTube.width,
            height: asset.youTube.height,
            previewUrl: asset.youTube.previewUrl,
            assetType: 'youtube',
        }
    } else if (asset.bunnyAsset) {
        return {
            url: asset.bunnyAsset,
            assetType: 'bunny',
        }
    } else {
        return {
            dummy: 'empty',
            assetType: 'empty',
        }
    }
}

export function mapToPost(postResponse: PostResponse): PostModel {
    return {
        id: postResponse.id,
        userId: postResponse.relationships.userId,
        title: postResponse.title,
        text: postResponse.text,
        textHtml: postResponse.textHtml,
        textDelta: postResponse.textDelta,
        publishedAt: postResponse.publishedAt ?? null,
        pinnedAt: postResponse.pinnedAt,
        state: mapState(postResponse.state),
        previewAssets: postResponse.previewAssets.map((asset) => mapPreviewAsset(asset)),
        assets: postResponse.assets.map((asset) => mapAsset(asset)),
        fullAssets: postResponse.fullAsset,
        counts: {
            comments: postResponse.counts.comments,
            replies: postResponse.counts.replies,
        },
        categories: postResponse.categories.map((category) => ({
            id: category.id,
            name: category.name,
            slug: category.slug,
        })),
        price: postResponse.price,
        ...(postResponse.savedPostInfo && {
            savedPostInfo: {
                savedAt: postResponse.savedPostInfo.savedAt,
                id: postResponse.savedPostInfo.id,
            },
        }),
        isSponsored: postResponse.isSponsored,
        isAgeRestricted: postResponse.isAgeRestricted,
        isExcludedFromRss: postResponse.excludeFromRss ?? undefined,
        myVote: postResponse.myVote,
        pollId: postResponse.pollId,
        voteScore: postResponse.voteScore,
        hasPreview: postResponse.hasPreview,
        hasPreviewInternal: postResponse.hasPreviewInternal,
    }
}

export function mapJsonApiToPost(postDto: PostDto): PostModel {
    if (!postDto.attributes.state || !postDto.id || !postDto.relationships.user?.id) {
        throw new Error(`Post ${JSON.stringify(postDto)} is missing state, id or userId`)
    }

    return {
        id: postDto.id,
        userId: postDto.relationships.user.id,
        text: postDto.attributes.text,
        textHtml: postDto.attributes.textHtml,
        textDelta: postDto.attributes.textDelta,
        publishedAt: postDto.attributes.publishedAt ?? null,
        pinnedAt: postDto.attributes.pinnedAt,
        state: mapState(postDto.attributes.state),
        previewAssets: [],
        assets: postDto.attributes.assets.map((asset) => mapAsset(asset)),
        fullAssets: postDto.attributes.fullAsset,
        counts: {
            comments: postDto.attributes.counts?.comments ?? 0,
            replies: postDto.attributes.counts?.replies ?? 0,
        },
        categories: [],
        price: postDto.attributes.price,
        isSponsored: postDto.attributes.isSponsored,
        isAgeRestricted: postDto.attributes.isAgeRestricted,
        myVote: 0,
        isExcludedFromRss: postDto.attributes.excludeFromRss ?? undefined,
        voteScore: 0,
        hasPreview: false,
        hasPreviewInternal: false,
    }
}

export function mapToComment(postResponse: PostResponse): CommentModel {
    if (!postResponse.relationships.parentId) {
        throw new Error(`Missing parentId for comment ${postResponse.id}`)
    }
    return {
        id: postResponse.id,
        userId: postResponse.relationships.userId,
        text: postResponse.text,
        textDelta: postResponse.textDelta,
        textHtml: postResponse.textHtml,
        publishedAt: postResponse.publishedAt ?? '',
        state: mapState(postResponse.state),
        assets: postResponse.assets.map((asset) => mapAsset(asset)),
        parentId: postResponse.relationships.parentId,
        siblingId: postResponse.relationships.siblingId,
        counts: {
            comments: postResponse.counts.comments,
            replies: postResponse.counts.replies,
        },
        myVote: postResponse.myVote,
        voteScore: postResponse.voteScore,
    }
}

export function mapToMessage(postResponse: PostResponse): MessageModel {
    return {
        id: postResponse.id,
        text: postResponse.text ?? '',
        textHtml: postResponse.textHtml,
        sentAt: postResponse.publishedAt,
        sentById: postResponse.relationships.userId,
        assets: postResponse.assets.map((asset) => mapAsset(asset)),
        fullAssets: postResponse.fullAsset,
        price: postResponse.price,
        messageThreadId: postResponse.relationships.messageThreadId ?? '',
    }
}

export function mapToUser(userResponse: UserResponse): UserModel {
    return {
        id: userResponse.id,
        name: userResponse.name,
        bio: userResponse.bio,
        path: userResponse.path,
        subscribable: userResponse.subscribable,
        verified: userResponse.verified,
        ...(userResponse.image && {
            image: {
                url: userResponse.image.id,
                height: userResponse.image.height,
                width: userResponse.image.width,
                hidden: userResponse.image.hidden,
            },
        }),
        counts: {
            supporters: userResponse.counts.supporters,
            supportersThreshold: userResponse.counts.supportersThreshold,
            supporting: userResponse.counts.supporting,
            posts: userResponse.counts.posts,
            ownedCommunities: userResponse.counts.ownedCommunities,
        },
        hasRssFeed: userResponse.hasRssFeed,
        tier: mapTier(userResponse.tier),
        categories: userResponse.categories,
        isDeleted: userResponse.isDeleted,
        privacyPolicyEnabled: userResponse.privacyPolicyEnabled,
        analytics: userResponse.analytics,
        hasGiftsAllowed: userResponse.hasGiftsAllowed,
        spotifyShowUri: userResponse.spotify?.spotifyUri,
        emailPublic: userResponse.emailPublic,
        profileType: userResponse.profileType,
    }
}

export function mapRole(role: Role): UserRole {
    switch (role) {
        case Role.MODERATOR:
            return UserRole.MODERATOR
        case Role.USER:
            return UserRole.USER
    }
}

function mapState(state: PostStateRest): PostState {
    switch (state) {
        case PostStateRest.DELETED:
            return PostState.DELETED
        case PostStateRest.PUBLISHED:
            return PostState.PUBLISHED
        case PostStateRest.PROCESSING:
            return PostState.PROCESSING
        case PostStateRest.REVISION:
            return PostState.REVISION
        case PostStateRest.SCHEDULED:
            return PostState.SCHEDULED
    }
}

export function mapCurrency(currency: CurrencyRest): Currency {
    switch (currency) {
        case CurrencyRest.EUR:
            return Currency.EUR
        case CurrencyRest.USD:
            return Currency.USD
        case CurrencyRest.CZK:
            return Currency.CZK
        case CurrencyRest.GBP:
            return Currency.GBP
        case CurrencyRest.PLN:
            return Currency.PLN
        case CurrencyRest.AUD:
            return Currency.AUD
        case CurrencyRest.CHF:
            return Currency.CHF
        case CurrencyRest.NZD:
            return Currency.NZD
        case CurrencyRest.RON:
            return Currency.RON
    }
}

export function mapSubscriptionStatus(status: SubscriptionsDtoStatus): SubscriptionStatus {
    switch (status) {
        case SubscriptionsDtoStatus.ACTIVE:
            return SubscriptionStatus.ACTIVE
        case SubscriptionsDtoStatus.PAST_DUE:
            return SubscriptionStatus.PAST_DUE
        case SubscriptionsDtoStatus.INACTIVE:
            return SubscriptionStatus.INACTIVE
    }
}

export function mapDocumentTypeToRest(type: PostDocumentType): DocumentTypeRest {
    switch (type) {
        case PostDocumentType.DOCX:
            return DocumentTypeRest.DOCX
        case PostDocumentType.PDF:
            return DocumentTypeRest.PDF
        case PostDocumentType.RAW:
            return DocumentTypeRest.RAW
        case PostDocumentType.XLSX:
            return DocumentTypeRest.XLSX
        case PostDocumentType.PPTX:
            return DocumentTypeRest.PPTX
        case PostDocumentType.GP5:
            return DocumentTypeRest.GP5
        case PostDocumentType.GPX:
            return DocumentTypeRest.GPX
        case PostDocumentType.MIDI:
            return DocumentTypeRest.MIDI
        case PostDocumentType.EPUB:
            return DocumentTypeRest.EPUB
        case PostDocumentType.ARW:
            return DocumentTypeRest.ARW
        case PostDocumentType.OBJ:
            return DocumentTypeRest.OBJ
        case PostDocumentType.FBX:
            return DocumentTypeRest.FBX
        case PostDocumentType.GLTF:
            return DocumentTypeRest.GLTF
        case PostDocumentType.GLB:
            return DocumentTypeRest.GLB
        case PostDocumentType.STL:
            return DocumentTypeRest.STL
        case PostDocumentType.PLY:
            return DocumentTypeRest.PLY
        case PostDocumentType.DAE:
            return DocumentTypeRest.DAE
        case PostDocumentType.BLEND:
            return DocumentTypeRest.BLEND
        case PostDocumentType._3DS:
            return DocumentTypeRest._3DS
    }
}

export function mapDocumentType(type: DocumentTypeRest): PostDocumentType {
    switch (type) {
        case DocumentTypeRest.DOCX:
            return PostDocumentType.DOCX
        case DocumentTypeRest.PDF:
            return PostDocumentType.PDF
        case DocumentTypeRest.RAW:
            return PostDocumentType.RAW
        case DocumentTypeRest.XLSX:
            return PostDocumentType.XLSX
        case DocumentType.PPTX:
            return PostDocumentType.PPTX
        case DocumentType.GP5:
            return PostDocumentType.GP5
        case DocumentType.GPX:
            return PostDocumentType.GPX
        case DocumentType.MIDI:
            return PostDocumentType.MIDI
        case DocumentType.EPUB:
            return PostDocumentType.EPUB
        case DocumentType.ARW:
            return PostDocumentType.ARW
        case DocumentType.OBJ:
            return PostDocumentType.OBJ
        case DocumentType.FBX:
            return PostDocumentType.FBX
        case DocumentType.GLTF:
            return PostDocumentType.GLTF
        case DocumentType.GLB:
            return PostDocumentType.GLB
        case DocumentType.STL:
            return PostDocumentType.STL
        case DocumentType.PLY:
            return PostDocumentType.PLY
        case DocumentType.DAE:
            return PostDocumentType.DAE
        case DocumentType.BLEND:
            return PostDocumentType.BLEND
        case DocumentType._3DS:
            return PostDocumentType._3DS
    }
}

export function mapNotificationType(type: NotificationTypeRest): NotificationType {
    switch (type) {
        case NotificationTypeRest.NEW_SUBSCRIPTION:
            return NotificationType.NEW_SUBSCRIPTION
        case NotificationTypeRest.SUBSCRIBE_REQUEST_ACCEPTED:
            return NotificationType.SUBSCRIBE_REQUEST_ACCEPTED
        case NotificationTypeRest.CANCELLED_SUBSCRIPTION_ENDED:
            return NotificationType.CANCELLED_SUBSCRIPTION_ENDED
        case NotificationTypeRest.CANCELLED_SUBSCRIPTION_INSUFFICIENT_FUNDS:
            return NotificationType.CANCELLED_SUBSCRIPTION_INSUFFICIENT_FUNDS
        case NotificationTypeRest.CANCELLED_SUBSCRIPTION_REFUSED:
            return NotificationType.CANCELLED_SUBSCRIPTION_REFUSED
        case NotificationTypeRest.CANCELLED_SUBSCRIPTION_REFUNDED:
            return NotificationType.CANCELLED_SUBSCRIPTION_REFUNDED
        case NotificationTypeRest.CANCELLED_SUBSCRIPTION_BY_CREATOR:
            return NotificationType.CANCELLED_SUBSCRIPTION_BY_CREATOR
        case NotificationTypeRest.CANCELLED_SUBSCRIPTION_OTHER:
            return NotificationType.CANCELLED_SUBSCRIPTION_OTHER
        case NotificationTypeRest.NEW_THREAD:
            return NotificationType.NEW_THREAD
        case NotificationTypeRest.NEW_POST:
            return NotificationType.NEW_POST
        case NotificationTypeRest.NEW_LIVESTREAM:
            return NotificationType.NEW_LIVESTREAM
        case NotificationTypeRest.NEW_COMMENT:
            return NotificationType.NEW_COMMENT
        case NotificationTypeRest.NEW_REPLY:
            return NotificationType.NEW_REPLY
        case NotificationTypeRest.NEW_REPLY_TO_REPLY:
            return NotificationType.NEW_REPLY_TO_REPLY
        case NotificationTypeRest.PAID_POST:
            return NotificationType.PAID_POST
        case NotificationTypeRest.PAYMENT_CARD_DECLINED:
            return NotificationType.PAYMENT_CARD_DECLINED
        case NotificationTypeRest.PAYMENT_INSUFFICIENT_FUNDS:
            return NotificationType.PAYMENT_INSUFFICIENT_FUNDS
        case NotificationTypeRest.PAYMENT_FAILED:
            return NotificationType.PAYMENT_FAILED
        case NotificationTypeRest.NEWSLETTER:
        case NotificationTypeRest.TERMS_CHANGED:
            throw new Error(`Unexpected notification type ${type}`)
    }
}

export function mapTier(tierResponse: TierResponse): TierModel {
    return {
        id: tierResponse.id,
        priceCents: tierResponse.priceCents,
        currency: mapCurrency(tierResponse.currency),
        default: tierResponse.default,
        hidden: tierResponse.hidden,
    }
}

export function mapToPoll(pollResponse: PollResponse): PollModel {
    return {
        id: pollResponse.id,
        deadline: pollResponse.deadline,
        options: pollResponse.options.map((option) => ({
            id: option.id,
            title: option.title,
            voteCount: option.voteCount,
            hasVotedFor: option.hasVotedFor,
        })),
    }
}

function mapGjirafaLivestreamStatus(status: LiveVideoStatus): GjirafaLivestreamStatus {
    switch (status) {
        case LiveVideoStatus.LIVE:
            return GjirafaLivestreamStatus.LIVE
        case LiveVideoStatus.OFFLINE:
            return GjirafaLivestreamStatus.OFFLINE
        case LiveVideoStatus.PROCESSING:
            return GjirafaLivestreamStatus.PROCESSING
        case LiveVideoStatus.INTERRUPTED:
            return GjirafaLivestreamStatus.INTERRUPTED
    }
}

function mapGjirafaStatus(status: GjirafaStatusRest): GjirafaQualityTypeStatus {
    switch (status) {
        case GjirafaStatusRest.PROCESSING:
            return GjirafaQualityTypeStatus.PROCESSING
        case GjirafaStatusRest.COMPLETE:
            return GjirafaQualityTypeStatus.COMPLETE
        case GjirafaStatusRest.ERROR:
            return GjirafaQualityTypeStatus.ERROR
        case GjirafaStatusRest.NOT_APPLICABLE:
            return GjirafaQualityTypeStatus.NOT_APPLICABLE
        case GjirafaStatusRest.PARTIALLY_COMPLETED:
            return GjirafaQualityTypeStatus.PARTIALLY_COMPLETED
    }
}

export function mapAssets(inputAssets: PostAssetInputWithYoutube[]): PostAssetInputRest[] {
    return inputAssets.map((asset) => {
        validateAsset(asset)
        if (asset.image) {
            return {
                image: {
                    ...asset.image,
                    hidden: false,
                },
            }
        } else if (asset.document) {
            return {
                document: {
                    name: asset.document.name,
                    url: asset.document.url,
                    type: mapDocumentTypeToRest(asset.document.type),
                },
                thumbnail: asset.document.thumbnailUrl,
                thumbnailImage:
                    (asset.document.thumbnail && {
                        ...asset.document.thumbnail,
                        hidden: false,
                    }) ??
                    undefined,
            }
        } else if (asset.gjirafa) {
            return {
                gjirafa: {
                    id: asset.gjirafa.id,
                },
                thumbnail: asset.gjirafa.thumbnailUrl,
                thumbnailImage:
                    (asset.gjirafa.thumbnail && {
                        ...asset.gjirafa.thumbnail,
                        hidden: false,
                    }) ??
                    undefined,
            }
        } else if (asset.gjirafaLivestream) {
            return {
                gjirafaLivestream: {
                    id: asset.gjirafaLivestream.id,
                },
                thumbnail: asset.gjirafaLivestream.thumbnailUrl,
                thumbnailImage:
                    (asset.gjirafaLivestream.thumbnail && {
                        ...asset.gjirafaLivestream.thumbnail,
                        hidden: false,
                    }) ??
                    undefined,
            }
        } else if (asset.youtube) {
            return {
                youtube: {
                    id: asset.youtube.id,
                    width: asset.youtube.width,
                    height: asset.youtube.height,
                    previewUrl: asset.youtube.previewUrl,
                },
            }
        } else {
            throw Error(`Failed to map asset ${JSON.stringify(asset)}`)
        }
    })
}

function validateAsset(asset: PostAssetInput) {
    const filledFields = Object.keys(asset).filter((field) => asset[field as keyof PostAssetInput])

    if (filledFields.length === 0) {
        throw new GraphQLError('At least one field must be defined for an asset', {
            extensions: { code: ApolloServerErrorCode.BAD_REQUEST },
        })
    }

    if (filledFields.length > 1) {
        throw new GraphQLError('Only one field can be defined for an asset', {
            extensions: { code: ApolloServerErrorCode.BAD_REQUEST },
        })
    }
}
