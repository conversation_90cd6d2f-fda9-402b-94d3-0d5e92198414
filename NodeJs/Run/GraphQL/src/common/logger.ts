import winston, { format } from 'winston'
import { LoggingWinston } from '@google-cloud/logging-winston'
import { currentEnv, Environment } from './environment'

export const logger = winston.createLogger({
    level: 'info',
    transports: [],
})

if (currentEnv === Environment.DEVEL || currentEnv === Environment.STAGING || currentEnv === Environment.PRODUCTION) {
    logger.add(
        new LoggingWinston({
            resource: {
                type: 'cloud_run_revision',
                labels: {
                    configuration_name: currentEnv,
                    service_name: `${currentEnv}-graphql`,
                    ...(process.env.CLOUD_REGION && { region: process.env.CLOUD_REGION }),
                    ...(process.env.CLOUD_REGION && { location: process.env.CLOUD_REGION }),
                },
            },
            logName: `${currentEnv}-graphql`,
        })
    )
} else {
    logger.add(
        new winston.transports.Console({
            format: format.combine(
                format.label({ label: currentEnv }),
                format.timestamp({ format: 'HH:mm:ss.SSS' }),
                format.colorize(),
                format.printf(({ level, message, label, timestamp }) => `${timestamp} ${level}: [${label}] ${message}`)
            ),
        })
    )
}
