import dotenv from 'dotenv'
import path from 'node:path'
import { z } from 'zod'

export enum Environment {
    LOCAL = 'local',
    TEST = 'test',
    DEVEL = 'devel',
    STAGING = 'staging',
    PRODUCTION = 'prod',
}

if (process.env.NODE_ENV === 'test') {
    dotenv.config({ path: path.resolve(process.cwd(), '.env.test') })
} else {
    dotenv.config()
}

const envSchema = z.object({
    GJIRAFA_API_KEY: z.string().trim(),
    GJIRAFA_PROJECT: z.string().trim(),
    HIVE_API_KEY: z.string().trim(),
    INTERNAL_API_KEY: z.string().trim(),
    NODE_ENV: z.enum(['local', 'test', 'devel', 'staging', 'production']),
})

export const validateEnvVariables = () => {
    const envServer = envSchema.safeParse({
        GJIRAFA_API_KEY: process.env.GJIRAFA_API_KEY,
        GJIRAFA_PROJECT: process.env.GJIRAFA_PROJECT,
        HIVE_API_KEY: process.env.HIVE_API_KEY,
        INTERNAL_API_KEY: process.env.INTERNAL_API_KEY,
        NODE_ENV: process.env.NODE_ENV,
    })

    if (!envServer.success) {
        console.error(envServer.error.issues)
        process.exit(1)
    }
}

validateEnvVariables()

let currentEnv: Environment
switch (process.env.NODE_ENV) {
    case 'local': {
        currentEnv = Environment.LOCAL
        break
    }
    case 'test': {
        currentEnv = Environment.TEST
        break
    }
    case 'devel': {
        currentEnv = Environment.DEVEL
        break
    }
    case 'staging': {
        currentEnv = Environment.STAGING
        break
    }
    case 'production': {
        currentEnv = Environment.PRODUCTION
        break
    }
}

type EnvSchemaType = z.infer<typeof envSchema>

declare global {
    // eslint-disable-next-line @typescript-eslint/no-namespace
    namespace NodeJS {
        // eslint-disable-next-line @typescript-eslint/no-empty-object-type, @typescript-eslint/consistent-type-definitions
        interface ProcessEnv extends EnvSchemaType {}
    }
}

export { currentEnv }
