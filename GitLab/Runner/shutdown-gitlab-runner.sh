#!/usr/bin/env bash

# Send stderr to GCE virtual serial port
exec 2>/dev/ttyS0
# Log everything and send to virtual serial port, https://urbanautomaton.com/blog/2014/09/09/redirecting-bash-script-output-to-syslog/
exec 1> >(logger -s -t shutdown-gitlab-runner.sh) 2>&1

set -x

# Gracefully stop GitLab runner Docker instance so that it properly aborts jobs, #27384:
docker stop -t 100 $(docker ps -q --filter name=gitlab-runner)
